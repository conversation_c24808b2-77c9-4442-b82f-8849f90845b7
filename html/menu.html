<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Menu</title>

    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- jQuery 3.7.1 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

    <style>

        .et_menu_container {
            width: 100%;
            padding-left: 25px;
            padding-right: 25px;
        }
        .logo_container {
            position: absolute;
            height: 100%;
            width: 90%;
        }
        .logo_helper {
            display: inline-block;
            height: 100%;
            vertical-align: middle;
            width: 0;
        }
        #et-top-navigation {
            padding-top: 11px !important;
            padding-left: 122px;
        }
        .mobile_menu_bar {
            position: relative;
            font-size: 20px;
            color: #000;
            cursor: pointer;
            margin-left: auto;
            display: block;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f5f5f5;
            overflow-x: hidden;
        }



        /* OVERWRITE ET BUILDER STYLES */

        #et_mobile_nav_menu .select_page {
            display: none;
        }

        @media all and (max-width: 980px) {
            #main-header {
                top: 0px !important;
                position: fixed !important;
                background-color: #ffffff !important;
                box-shadow:  none !important;
                line-height: 23px;
                font-weight: 500;
                width: 100%;
                z-index: 999999;
                height: 60px;
            }
            #page-container {
                padding-top: 60px !important;
            }
            #et-top-navigation {
                padding-top: 17px !important;
                padding-bottom: 17px !important;
            }
        }

        /* MOBILE MENU STYLES */

        .mobile-menu {
            position: fixed;
            top: 0;
            left: 100%;
            width: 100%;
            height: 100vh;
            background-color: #fff;
            overflow: hidden;
            z-index: 9999;
            display: none;
            transition: transform 0.3s ease-out;
        }

        .mobile-menu--visible {
            display: block;
        }

        /* Prevent body scroll when menu is open and slide background content */
        body.mobile-menu-open {
            overflow: hidden;
        }

        /* Slide background content to the left when menu is open */
        body.mobile-menu-open #et-main-area footer div.et_pb_section.et_pb_section_2_tb_footer,
        body.mobile-menu-open #main-content,
        body.mobile-menu-open #main-header{
            transform: translateX(-100%);
            transition: transform 0.3s ease-out !important;
        }

        body.mobile-menu-open #et-main-area footer div.et_pb_section.et_pb_section_3_tb_footer {
            transform: translateX(-150%);
            transition: transform 0.3s ease-out !important;
        }

        /* Reset background content position when menu is closed */
        body:not(.mobile-menu-open) #et-main-area footer div.et_pb_section.et_pb_section_2_tb_footer,
        body:not(.mobile-menu-open) #main-content,
        body:not(.mobile-menu-open) #main-header{
            transform: translateX(0);
            transition: transform 0.3s ease-out !important;
        }

        body:not(.mobile-menu-open) #et-main-area footer div.et_pb_section.et_pb_section_3_tb_footer {
            transform: translateX(-50%);
            transition: transform 0.3s ease-out !important;
        }

        .mobile-menu__button {
            color: #08243E;
            padding: 3px;
            display: flex;
            background: none;
            border: none;
            font-size: 20px;
            margin-left: auto;
            position: relative;
        }

        .mobile-menu__header {
            background-color: #08243E;
            color: white;
            padding: 0 25px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: 60px;
        }

        .mobile-menu__status-icons {
            display: flex;
            align-items: center;
            margin-right: 40px;
            gap: 18px;
        }

        .mobile-menu__status-icons a {
            color: inherit;
            line-height: 0;
            text-decoration: none;
        }

        .mobile-menu__status-icons i {
            font-size: 20px;
            cursor: pointer;
        }

        .mobile-menu__close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
        }

        .mobile-menu__container {
            position: relative;
            height: calc(100vh - 60px - 85px); /* Subtract header height and footer height from viewport height */
            overflow-y: auto;
            overflow-x: hidden;
        }

        .mobile-menu__level {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #fff;
            display: flex;
            flex-direction: column;
            transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            will-change: transform;
        }

        /* Initial positions */
        .mobile-menu__level {
            transform: translateX(100%);
        }

        .mobile-menu__level--active {
            transform: translateX(0);
        }

        /* Animation states */
        .mobile-menu__level--slide-out-left {
            transform: translateX(-100%);
        }

        .mobile-menu__level--slide-out-right {
            transform: translateX(100%);
        }

        .mobile-menu__level--slide-in-from-right {
            transform: translateX(0);
        }

        .mobile-menu__level--slide-in-from-left {
            transform: translateX(0);
        }

        .mobile-menu__search {
            padding: 39px 23px;
            background-color: #f8f9fa;
            position: relative;
        }

        .mobile-menu__search-input {
            width: 100%;
            height: 40px;
            padding: 12px 56px 12px 24px !important;
            border: none !important;
            border-radius: 25px;
            background-color: #E9E9E9 !important;
            font-size: 12px;
            outline: none;
        }

        .mobile-menu__search-btn {
            position: absolute;
            right: 50px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            padding: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mobile-menu__search-icon {
            color: #000;
            font-size: 16px;
        }

        .mobile-menu__content {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 10px; /* Add some padding to prevent content from touching the footer */
        }

        .mobile-menu__item {
            display: flex;
            align-items: center;
            padding: 16px 23px;
            height: 70px;
            border-bottom: 1px solid #C1C1C1;
            cursor: pointer;
            text-decoration: none;
            color: #000;
        }

        .mobile-menu__item-icon {
            margin-right: 10px;
            font-size: 18px;
            color: #000;
            width: 24px;
            text-align: center;
        }

        .mobile-menu__item-text {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
        }

        .mobile-menu__item-arrow {
            color: #000;
            font-size: 14px;
        }

        .mobile-menu__level-header {
            display: flex;
            align-items: center;
            padding: 20px;
            height: 104px;
            border-bottom: 1px solid #C1C1C1;
            background-color: #fff;
        }

        .mobile-menu__back-btn {
            background: none;
            border: none;
            font-size: 18px;
            color: #000;
            cursor: pointer;
            margin-right: 15px;
            padding: 5px;
        }

        .mobile-menu__title {
            font-size: 20px;
            font-weight: 600;
            color: #000;
            display: flex;
            align-items: center;
        }

        .mobile-menu__title-icon {
            margin-right: 12px;
            font-size: 18px;
        }

        .mobile-menu__footer-link {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: #1e3a5f linear-gradient(179.34deg, #002856 -78.97%, #00AAFF 196.03%);
            padding: 24px;
            height: 85px;
            display: flex;;
            z-index: 10;
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            align-items: center;
            justify-content: space-between;
        }

        .mobile-menu__submenu-item {
            display: flex;
            align-items: center;
            padding: 16px 27px;
            height: 70px;
            border-bottom: 1px solid #C1C1C1;
            cursor: pointer;
            text-decoration: none;
            color: #000;
        }

        .mobile-menu__submenu-item-text {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
        }

        .mobile-menu__submenu-item-arrow {
            color: #000;
            font-size: 14px;
        }

        /* Disable transitions during setup */
        .mobile-menu__level--no-transition {
            transition: none !important;
        }

        /* Ensure smooth animations */
        .mobile-menu__level--animating {
            pointer-events: none;
        }

        @supports (height: 100dvh) {
            .mobile-menu {
                height: 100dvh;
            }

            .mobile-menu__container {
                height: calc(100dvh - 60px - 85px); /* Subtract header height and footer height from viewport height */
            }
        }

        /* Use container queries in modern browsers */
        @supports (container-type: size) {
            .mobile-menu {
                container-type: size;
                container-name: mobileMenu;
            }
        }

        @media all and (max-height: 755px) {
            .mobile-menu__search {
                padding: 23px;
            }
        }

        /* Use container queries in modern browsers */
        @supports (container-type: size) {
            @container mobileMenu (height < 755px) {
                .mobile-menu__search {
                    padding: 23px;
                }
            }
        }

        @media all and (max-height: 730px) {
            .mobile-menu__item {
                padding: 6px 23px;
                height: 60px;
            }
        }

        @supports (container-type: size) {
            @container mobileMenu (height < 730px) {
                .mobile-menu__item {
                    padding: 6px 23px;
                    height: 60px;
                }
            }
        }

        @media all and (max-height: 645px) {
            .mobile-menu__item {
                height: 50px;
            }
            .mobile-menu__item-text {
                font-size: 14px;
            }
        }

        @supports (container-type: size) {
            @container mobileMenu (height < 645px) {
                .mobile-menu__item {
                    height: 50px;
                }
                .mobile-menu__item-text {
                    font-size: 14px;
                }
            }
        }

        @media all and (max-height: 590px) {
            .mobile-menu__footer-link {
                height: 50px;
            }

            .mobile-menu__container {
                height: calc(100vh - 60px - 50px); /* Subtract header height and footer height from viewport height */
            }

            @supports (height: 100dvh) {
                .mobile-menu__container {
                    height: calc(100dvh - 60px - 50px); /* Subtract header height and footer height from viewport height */
                }
            }
        }

        @supports (container-type: size) {
            @container mobileMenu (height < 590px) {
                .mobile-menu__footer-link {
                    height: 50px;
                }

                .mobile-menu__container {
                    height: calc(100vh - 60px - 50px); /* Subtract header height and footer height from viewport height */
                }

                @supports (height: 100dvh) {
                    .mobile-menu__container {
                        height: calc(100dvh - 60px - 50px); /* Subtract header height and footer height from viewport height */
                    }
                }
            }
        }

    </style>
</head>
<body>
<div id="page-content">
    <ul id="et-secondary-nav" style="display: none;" class="menu"><li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-960"><a href="/search"><i class="fa-solid fa-magnifying-glass" aria-hidden="true"></i> Search</a></li>
        <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-959"><a href="#"><i class="fa-solid fa-globe" aria-hidden="true"></i> Region</a>
            <ul class="sub-menu">
                <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-home menu-item-952"><a href="https://cadev.deployedweb.com/">North America (English)</a></li>
                <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-25835724"><a href="https://canadafr.commandalkon.com/">Canada (Français)</a></li>
                <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-953"><a href="https://latam.commandalkon.com/">LATAM (Español)</a></li>
                <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-1188"><a href="https://brazil.commandalkon.com/">Brazil (Português)</a></li>
                <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-1189"><a href="https://apac.commandalkon.com/">APAC (English)</a></li>
                <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-1190"><a href="https://emea.commandalkon.com/">EMEA (English)</a></li>
                <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-1191"><a href="https://netherlands.commandalkon.com/">Netherlands (Dutch)</a></li>
                <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-250485"><a href="https://dach.commandalkon.com/">DACH (Deutsch)</a></li>
                <li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-1192"><a href="https://france.commandalkon.com/">France (Français)</a></li>
            </ul>
        </li>
    </ul>
<header id="main-header">
    <div class="container clearfix et_menu_container">
        <div class="logo_container">
            <span class="logo_helper"></span>
            <a href="https://commandalkon.com/">
                <img src="https://commandalkon.com/wp-content/uploads/2023/02/new_logo.png" width="92" alt="Command Alkon USA" id="logo" data-height-percentage="50" data-actual-width="390" data-actual-height="84.6328">
            </a>
        </div>

        <div id="et-top-navigation">
            <div id="et_mobile_nav_menu">
                <span class="select_page"><?php esc_html_e('Select Page', 'Divi'); ?></span>
                <button class="mobile-menu__button">
                    <i class="fas fa-bars"></i>
                </button>

                <div class="mobile-menu">
                    <!-- Menu Header -->
                    <div class="mobile-menu__header">
                        <div class="mobile-menu__status-icons">
                            <a href="/login/">
                                <i class="fa fa-user-circle" style="font-size: 20px;"></i>
                            </a>
                            <i class="fa fa-globe" style="font-size: 18px;"></i>
                        </div>
                        <button class="mobile-menu__close-btn" aria-label="Close menu">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="mobile-menu__container">
                        <!-- Level 1 - Main Menu -->
                        <div class="mobile-menu__level mobile-menu__level--level-1 mobile-menu__level--active" id="level1">
                            <!-- Search -->
                            <form class="mobile-menu__search" method="GET" action="/">
                                <input type="text" class="mobile-menu__search-input" placeholder="Search" name="s" aria-label="Search">
                                <input type="hidden" name="et_pb_searchform_submit" value="">
                                <input type="hidden" name="et_pb_include_posts" value="yes">
                                <input type="hidden" name="et_pb_include_pages" value="yes">
                                <button type="submit" class="mobile-menu__search-btn" aria-label="Search">
                                    <i class="fas fa-search mobile-menu__search-icon"></i>
                                </button>
                            </form>

                            <div class="mobile-menu__content">
                                <a href="#" class="mobile-menu__item" data-target="products" role="menuitem" aria-haspopup="true">
                                    <i class="fas fa-laptop mobile-menu__item-icon"></i>
                                    <span class="mobile-menu__item-text">Products</span>
                                    <i class="fas fa-chevron-right mobile-menu__item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__item" data-target="services" role="menuitem" aria-haspopup="true">
                                    <i class="fas fa-cogs mobile-menu__item-icon"></i>
                                    <span class="mobile-menu__item-text">Services</span>
                                    <i class="fas fa-chevron-right mobile-menu__item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__item" data-target="resources" role="menuitem" aria-haspopup="true">
                                    <i class="fas fa-book mobile-menu__item-icon"></i>
                                    <span class="mobile-menu__item-text">Resources</span>
                                    <i class="fas fa-chevron-right mobile-menu__item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__item" data-target="partners" role="menuitem" aria-haspopup="true">
                                    <i class="fas fa-handshake mobile-menu__item-icon"></i>
                                    <span class="mobile-menu__item-text">Partners</span>
                                    <i class="fas fa-chevron-right mobile-menu__item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__item" data-target="overview" role="menuitem" aria-haspopup="true">
                                    <i class="fas fa-chart-bar mobile-menu__item-icon"></i>
                                    <span class="mobile-menu__item-text">Overview</span>
                                    <i class="fas fa-chevron-right mobile-menu__item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__item" data-target="events" role="menuitem" aria-haspopup="true">
                                    <i class="fas fa-calendar mobile-menu__item-icon"></i>
                                    <span class="mobile-menu__item-text">Events</span>
                                    <i class="fas fa-chevron-right mobile-menu__item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__item" data-target="contact" role="menuitem">
                                    <i class="fas fa-envelope mobile-menu__item-icon"></i>
                                    <span class="mobile-menu__item-text">Contact Us</span>
                                    <i class="fas fa-chevron-right mobile-menu__item-arrow"></i>
                                </a>
                            </div>
                        </div>

                        <!-- Level 2 - Products Menu -->
                        <div class="mobile-menu__level mobile-menu__level--level-2" id="level2-products">
                            <div class="mobile-menu__level-header">
                                <button class="mobile-menu__back-btn" aria-label="Back to main menu">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <div class="mobile-menu__title">
                                    <i class="fas fa-laptop mobile-menu__title-icon"></i>
                                    Products
                                </div>
                            </div>

                            <div class="mobile-menu__content">
                                <a href="#" class="mobile-menu__submenu-item" data-target="ready-mix" role="menuitem" aria-haspopup="true">
                                    <span class="mobile-menu__submenu-item-text">Ready Mix</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" data-target="concrete-products" role="menuitem" aria-haspopup="true">
                                    <span class="mobile-menu__submenu-item-text">Concrete Products</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Asphalt</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Aggregates</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>
                            </div>
                        </div>

                        <!-- Level 2 - Services Menu -->
                        <div class="mobile-menu__level mobile-menu__level--level-2" id="level2-services">
                            <div class="mobile-menu__level-header">
                                <button class="mobile-menu__back-btn" aria-label="Back to main menu">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <div class="mobile-menu__title">
                                    <i class="fas fa-cogs mobile-menu__title-icon"></i>
                                    Services
                                </div>
                            </div>

                            <div class="mobile-menu__content">
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Consulting</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Technical Support</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Training</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Maintenance</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>
                            </div>
                        </div>

                        <!-- Level 2 - Resources Menu -->
                        <div class="mobile-menu__level mobile-menu__level--level-2" id="level2-resources">
                            <div class="mobile-menu__level-header">
                                <button class="mobile-menu__back-btn" aria-label="Back to main menu">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <div class="mobile-menu__title">
                                    <i class="fas fa-book mobile-menu__title-icon"></i>
                                    Resources
                                </div>
                            </div>

                            <div class="mobile-menu__content">
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Documentation</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Case Studies</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">White Papers</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Downloads</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>
                            </div>
                        </div>

                        <!-- Level 2 - Partners Menu -->
                        <div class="mobile-menu__level mobile-menu__level--level-2" id="level2-partners">
                            <div class="mobile-menu__level-header">
                                <button class="mobile-menu__back-btn" aria-label="Back to main menu">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <div class="mobile-menu__title">
                                    <i class="fas fa-handshake mobile-menu__title-icon"></i>
                                    Partners
                                </div>
                            </div>

                            <div class="mobile-menu__content">
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Technology Partners</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Channel Partners</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Integration Partners</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Become a Partner</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>
                            </div>
                        </div>

                        <!-- Level 2 - Overview Menu -->
                        <div class="mobile-menu__level mobile-menu__level--level-2" id="level2-overview">
                            <div class="mobile-menu__level-header">
                                <button class="mobile-menu__back-btn" aria-label="Back to main menu">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <div class="mobile-menu__title">
                                    <i class="fas fa-chart-bar mobile-menu__title-icon"></i>
                                    Overview
                                </div>
                            </div>

                            <div class="mobile-menu__content">
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Company Overview</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Our Mission</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Leadership Team</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Careers</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>
                            </div>
                        </div>

                        <!-- Level 2 - Events Menu -->
                        <div class="mobile-menu__level mobile-menu__level--level-2" id="level2-events">
                            <div class="mobile-menu__level-header">
                                <button class="mobile-menu__back-btn" aria-label="Back to main menu">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <div class="mobile-menu__title">
                                    <i class="fas fa-calendar mobile-menu__title-icon"></i>
                                    Events
                                </div>
                            </div>

                            <div class="mobile-menu__content">
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Upcoming Events</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Webinars</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Conferences</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Past Events</span>
                                    <i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>
                                </a>
                            </div>
                        </div>

                        <!-- Level 2 - Regions Menu -->
                        <div class="mobile-menu__level mobile-menu__level--level-2" id="level2-regions">
                            <div class="mobile-menu__level-header">
                                <button class="mobile-menu__back-btn" aria-label="Back to main menu">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <div class="mobile-menu__title">
                                    <i class="fas fa-globe mobile-menu__title-icon"></i>
                                </div>
                            </div>

                            <div class="mobile-menu__content">
                            </div>
                        </div>

                        <!-- Level 3 - Concrete Products Menu -->
                        <div class="mobile-menu__level mobile-menu__level--level-3" id="level3-concrete-products">
                            <div class="mobile-menu__level-header">
                                <button class="mobile-menu__back-btn" aria-label="Back to products menu">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <div class="mobile-menu__title">
                                    Concrete Products
                                </div>
                            </div>

                            <div class="mobile-menu__content">
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">COMMANDbatch CP</span>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Marcotte Batch CP</span>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Precast Solutions</span>
                                </a>

                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Quality Control</span>
                                </a>
                            </div>
                        </div>

                        <!-- Level 3 - Ready Mix Menu -->
                        <div class="mobile-menu__level mobile-menu__level--level-3" id="level3-ready-mix">
                            <div class="mobile-menu__level-header">
                                <button class="mobile-menu__back-btn" aria-label="Back to products menu">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <div class="mobile-menu__title">
                                    Ready Mix
                                </div>
                            </div>

                            <div class="mobile-menu__content">
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Material Supply</span>
                                </a>
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Ticket Accounting</span>
                                </a>
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Batch</span>
                                </a>
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Batch AI</span>
                                </a>
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">COMMANDbatch</span>
                                </a>
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Marcotte Batch</span>
                                </a>
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Load Assurance</span>
                                </a>
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Sales and Quoting</span>
                                </a>
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Dispatch</span>
                                </a>
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Customer Portal</span>
                                </a>
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Customer Portal</span>
                                </a>
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">TrackIt</span>
                                </a>
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Payments</span>
                                </a>
                                <a href="#" class="mobile-menu__submenu-item" role="menuitem">
                                    <span class="mobile-menu__submenu-item-text">Ticket Portal</span>
                                </a>

                            </div>
                        </div>
                    </div>

                    <!-- Single shared footer for all menu levels -->
                    <a href="#" class="mobile-menu__footer-link">
                        Learn About Command Cloud
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>


            </div>
        </div> <!-- #et-top-navigation -->
    </div> <!-- .container -->
</header>
    <div id="et-main-area">
        <div>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. A ad alias animi, blanditiis commodi culpa cumque debitis doloribus exercitationem fugiat id incidunt ipsam itaque magni minima minus modi nesciunt officia placeat quam quas qui quod reiciendis, sed, totam ullam veritatis voluptatibus! Aliquid dolore dolorum explicabo fugiat fugit, iste maiores nam perspiciatis quaerat quia ratione rem voluptatem. Adipisci consectetur consequuntur dolore eaque esse et eveniet excepturi fugiat magnam nemo, officiis praesentium repudiandae tempore vel velit? Aliquid atque aut beatae blanditiis consectetur consequatur corporis culpa deleniti dolor dolorem earum eos eveniet ex explicabo harum in maiores modi mollitia nam necessitatibus nihil, non obcaecati odio porro possimus praesentium quas quasi quibusdam quo quod ratione recusandae repellendus, repudiandae tenetur veniam veritatis voluptatem. Commodi cum dignissimos magni praesentium quas quidem quo quod repellat sint! Aperiam deserunt, eaque exercitationem fugit officiis perspiciatis porro quibusdam sunt! Accusamus impedit obcaecati placeat rem veniam! Adipisci debitis hic optio temporibus vitae! At doloremque esse eum explicabo quam quos recusandae sint temporibus vero! Commodi culpa dolorem, eligendi eos eveniet iste molestiae nisi odit recusandae rerum, saepe sapiente sunt voluptatem! Aut cumque eveniet excepturi harum ipsa nisi non placeat! Assumenda delectus dignissimos eaque eveniet molestias necessitatibus nemo quis reprehenderit! Dolorum et exercitationem inventore, labore mollitia nam perferendis sed sequi suscipit velit. Consequatur delectus dolore et, quaerat quis ratione repudiandae rerum sapiente sunt, tenetur voluptate voluptatum? Dolorem ea, exercitationem quam sequi sint ullam ut? Adipisci aliquid amet asperiores atque blanditiis consequuntur cum delectus dolore dolorem dolores dolorum ea eligendi eum, excepturi expedita id in iste, magnam magni molestiae necessitatibus odit omnis pariatur quaerat quia rem sit suscipit tempore ut veniam vero vitae voluptas voluptates. Aliquid amet aperiam aspernatur atque aut autem commodi consequatur deserunt dignissimos dolores ducimus ea ex expedita facilis id in libero magni natus nemo nisi nobis numquam obcaecati odio, officiis, omnis optio pariatur perspiciatis placeat possimus praesentium quae quaerat quasi, reiciendis repellendus rerum saepe veritatis. Aut enim esse fuga, iure laboriosam maxime obcaecati! Commodi culpa delectus, dolor incidunt iure pariatur porro quasi qui unde voluptate? Accusamus amet minima quas quidem quod quos rem! Blanditiis deleniti deserunt dolorem eligendi hic iste necessitatibus nostrum provident quas sapiente sed, unde? Autem consequuntur explicabo magni, necessitatibus porro sapiente soluta unde vel. Alias assumenda consectetur dicta magnam suscipit. At consectetur consequatur cum, doloremque ducimus eligendi eos esse est, exercitationem inventore libero neque nulla obcaecati optio placeat, porro provident quas quasi quibusdam soluta? Ab architecto asperiores commodi, impedit minus molestiae omnis repudiandae? Aperiam expedita facere, illum ipsa labore neque sit. A blanditiis delectus dolores eos, harum laborum recusandae totam. Aliquam aperiam asperiores aspernatur autem blanditiis commodi debitis dicta dolorum eveniet exercitationem expedita illo impedit ipsam iste itaque libero nam natus recusandae repellat saepe similique, sint temporibus voluptate. Accusantium consequuntur dolor ea harum id illum incidunt necessitatibus nihil perferendis, perspiciatis sit tenetur voluptates? Accusantium ad animi, atque culpa cupiditate delectus dolore est eum eveniet excepturi facere id inventore ipsam labore molestiae nemo odio omnis quae quasi quidem quis rerum sit suscipit tempore unde vitae voluptas voluptatem? Esse!</p>
        </div>
    </div>

</div>


    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let currentLevel = 1;
            let levelHistory = [{level: 1, id: 'level1'}];
            let isAnimating = false;

            // Helper functions for DOM manipulation

            const addClass = (element, className) => {
                if (element) element.classList.add(className);
            };

            const removeClass = (element, className) => {
                if (element) element.classList.remove(className);
            };

            const toggleClass = (element, className) => {
                if (element) element.classList.toggle(className);
            };

            const hasClass = (element, className) => {
                return element ? element.classList.contains(className) : false;
            };

            // Initialize menu - set level1 as active, others hidden
            function initializeMenu() {
                const menuLevels = document.querySelectorAll('.mobile-menu__level');

                menuLevels.forEach(level => {
                    removeClass(level, 'mobile-menu__level--active');
                    removeClass(level, 'mobile-menu__level--no-transition');
                    addClass(level, 'mobile-menu__level--no-transition');
                });

                // Set initial positions without animation
                const level1 = document.querySelector('#level1');
                if (level1) {
                    level1.style.transform = 'translateX(0)';
                    addClass(level1, 'mobile-menu__level--active');
                }

                menuLevels.forEach(level => {
                    if (level.id !== 'level1') {
                        level.style.transform = 'translateX(100%)';
                    }
                });

                // Re-enable transitions after a brief delay
                setTimeout(() => {
                    menuLevels.forEach(level => {
                        removeClass(level, 'mobile-menu__level--no-transition');
                    });
                }, 50);
            }

            // Menu navigation function with improved animation
            function navigateToLevel(targetLevel, targetId, direction = 'forward') {
                if (isAnimating) return; // Prevent multiple animations

                const currentLevelElement = document.querySelector('.mobile-menu__level--active');
                const targetLevelElement = document.querySelector('#' + targetId);

                if (!targetLevelElement || currentLevelElement?.id === targetId) {
                    return; // Target doesn't exist or is already active
                }

                isAnimating = true;

                // Add animating class to prevent interactions
                const menuLevels = document.querySelectorAll('.mobile-menu__level');
                menuLevels.forEach(level => {
                    addClass(level, 'mobile-menu__level--animating');
                });

                if (direction === 'forward') {
                    // Forward navigation: current slides left, new slides in from right

                    // Position target element off-screen to the right
                    addClass(targetLevelElement, 'mobile-menu__level--no-transition');
                    targetLevelElement.style.transform = 'translateX(100%)';

                    // Re-enable transition and start animation
                    setTimeout(() => {
                        removeClass(targetLevelElement, 'mobile-menu__level--no-transition');

                        // Animate current level out to the left
                        if (currentLevelElement) {
                            currentLevelElement.style.transform = 'translateX(-100%)';
                        }

                        // Animate target level in from the right
                        targetLevelElement.style.transform = 'translateX(0)';

                        // Update active states
                        if (currentLevelElement) {
                            removeClass(currentLevelElement, 'mobile-menu__level--active');
                        }
                        addClass(targetLevelElement, 'mobile-menu__level--active');
                    }, 10);

                    // Add to history
                    levelHistory.push({level: targetLevel, id: targetId});

                } else {
                    // Backward navigation: current slides right, previous slides in from left

                    // Position target element off-screen to the left
                    addClass(targetLevelElement, 'mobile-menu__level--no-transition');
                    targetLevelElement.style.transform = 'translateX(-100%)';

                    // Re-enable transition and start animation
                    setTimeout(() => {
                        removeClass(targetLevelElement, 'mobile-menu__level--no-transition');

                        // Animate current level out to the right
                        if (currentLevelElement) {
                            currentLevelElement.style.transform = 'translateX(100%)';
                        }

                        // Animate target level in from the left
                        targetLevelElement.style.transform = 'translateX(0)';

                        // Update active states
                        if (currentLevelElement) {
                            removeClass(currentLevelElement, 'mobile-menu__level--active');
                        }
                        addClass(targetLevelElement, 'mobile-menu__level--active');
                    }, 10);
                }

                currentLevel = targetLevel;

                // Clean up after animation completes
                setTimeout(() => {
                    isAnimating = false;
                    menuLevels.forEach(level => {
                        removeClass(level, 'mobile-menu__level--animating');
                    });

                    // Reset positions of non-active levels
                    menuLevels.forEach(level => {
                        if (!hasClass(level, 'mobile-menu__level--active')) {
                            addClass(level, 'mobile-menu__level--no-transition');
                            level.style.transform = 'translateX(100%)';
                        }
                    });

                    setTimeout(() => {
                        menuLevels.forEach(level => {
                            removeClass(level, 'mobile-menu__level--no-transition');
                        });
                    }, 10);
                }, 320); // Slightly longer than transition duration
            }

            // Function to dynamically generate region menu items from #et-secondary-nav
            function generateRegionMenuItems() {
                const secondaryNav = document.querySelector('#et-secondary-nav');
                const regionsMenuContent = document.querySelector('#level2-regions .mobile-menu__content');
                const regionsMenuTitle = document.querySelector('#level2-regions .mobile-menu__title');

                if (!secondaryNav || !regionsMenuContent) {
                    console.warn('Secondary nav or regions menu content not found');
                    return;
                }

                // Find the region menu item (contains globe icon or "Region" text)
                const regionMenuItem = Array.from(secondaryNav.querySelectorAll('li.menu-item-has-children')).find(item => {
                    const link = item.querySelector('a');
                    return link && (
                        link.textContent.toLowerCase().includes('region') ||
                        link.querySelector('.fa-globe')
                    );
                });

                if (!regionMenuItem) {
                    console.warn('Region menu item not found in secondary nav');
                    return;
                }

                const subMenu = regionMenuItem.querySelector('.sub-menu');
                if (!subMenu) {
                    console.warn('Region submenu not found');
                    return;
                }

                // Extract the title text from the parent menu item link
                const regionMenuLink = regionMenuItem.querySelector('a');
                if (regionMenuLink && regionsMenuTitle) {
                    // Get the text content, excluding any icon text
                    let titleText = regionMenuLink.textContent.trim();

                    // Remove any icon text that might be included (like from screen readers)
                    titleText = titleText.replace(/^\s*\S+\s+/, '').trim(); // Remove first word if it looks like an icon

                    // If the text still seems to contain icon artifacts, try a more specific approach
                    const iconElement = regionMenuLink.querySelector('i');
                    if (iconElement) {
                        // Clone the link, remove the icon, and get clean text
                        const tempLink = regionMenuLink.cloneNode(true);
                        const tempIcon = tempLink.querySelector('i');
                        if (tempIcon) {
                            tempIcon.remove();
                        }
                        titleText = tempLink.textContent.trim();
                    }

                    // Update the mobile menu title while preserving the icon
                    const titleIcon = regionsMenuTitle.querySelector('.mobile-menu__title-icon');
                    if (titleIcon) {
                        regionsMenuTitle.innerHTML = '';
                        regionsMenuTitle.appendChild(titleIcon);
                        regionsMenuTitle.appendChild(document.createTextNode('\n                                    ' + titleText));
                    } else {
                        // Fallback if icon is not found - preserve any existing icon HTML
                        const iconHTML = regionsMenuTitle.innerHTML.match(/<i[^>]*><\/i>/);
                        if (iconHTML) {
                            regionsMenuTitle.innerHTML = iconHTML[0] + '\n                                    ' + titleText;
                        } else {
                            regionsMenuTitle.textContent = titleText;
                        }
                    }
                }

                // Extract region items from submenu
                const regionItems = subMenu.querySelectorAll('li.menu-item a');

                if (regionItems.length === 0) {
                    console.warn('No region items found in submenu');
                    return;
                }

                // Generate mobile menu HTML structure
                let mobileMenuHTML = '';
                regionItems.forEach(item => {
                    const href = item.getAttribute('href') || '#';
                    const text = item.textContent.trim();

                    mobileMenuHTML += `
                        <a href="${href}" class="mobile-menu__submenu-item" role="menuitem">
                            <span class="mobile-menu__submenu-item-text">${text}</span>
                        </a>`;
                });

                // Replace the content in the regions menu
                regionsMenuContent.innerHTML = mobileMenuHTML;

                console.log('Region menu items generated successfully:', regionItems.length, 'items');
            }

            // Initialize menu on load
            initializeMenu();

            // Generate dynamic region menu items
            generateRegionMenuItems();

            // Handle mobile menu toggle button click (show menu)
            const toggleButton = document.querySelector('.mobile-menu__button');
            if (toggleButton) {
                toggleButton.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Show menu first, then trigger animation
                    const mobileMenu = document.querySelector('.mobile-menu');
                    if (mobileMenu) {
                        mobileMenu.style.display = 'block';
                        addClass(document.body, 'mobile-menu-open');

                        // Trigger animation on next frame
                        requestAnimationFrame(() => {
                            addClass(mobileMenu, 'mobile-menu--visible');
                        });

                        console.log('Menu opened');
                    }
                });
            }

            // Handle clicking outside menu to close it
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                mobileMenu.addEventListener('click', function(e) {
                    // Only close if clicking on the backdrop (the menu itself, not its children)
                    if (e.target === this) {
                        const closeBtn = document.querySelector('.mobile-menu__close-btn');
                        if (closeBtn) closeBtn.click();
                    }
                });
            }

            // Handle globe icon click to navigate to regions menu
            const globeIcon = document.querySelector('.mobile-menu__status-icons .fa-globe');
            if (globeIcon) {
                globeIcon.addEventListener('click', function(e) {
                    e.preventDefault();
                    const regionsLevel = document.querySelector('#level2-regions');
                    if (!isAnimating && regionsLevel) {
                        navigateToLevel(2, 'level2-regions', 'forward');
                        console.log('Navigated to regions menu');
                    }
                });
            }

            // Handle main menu item clicks
            const menuItems = document.querySelectorAll('.mobile-menu__item[data-target]');
            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = this.getAttribute('data-target');
                    const targetId = 'level2-' + target;

                    if (document.querySelector('#' + targetId)) {
                        navigateToLevel(2, targetId, 'forward');
                    }
                });
            });

            // Handle submenu item clicks for level 3
            const submenuItems = document.querySelectorAll('.mobile-menu__submenu-item[data-target]');
            submenuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = this.getAttribute('data-target');
                    const targetId = 'level3-' + target;

                    if (document.querySelector('#' + targetId)) {
                        navigateToLevel(3, targetId, 'forward');
                    }
                });
            });

            // Handle back button clicks
            const backButtons = document.querySelectorAll('.mobile-menu__back-btn');
            backButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();

                    if (isAnimating || levelHistory.length <= 1) return;

                    // Remove current level from history
                    levelHistory.pop();

                    // Get previous level info
                    const previousLevelInfo = levelHistory[levelHistory.length - 1];

                    if (previousLevelInfo) {
                        navigateToLevel(previousLevelInfo.level, previousLevelInfo.id, 'backward');
                    }
                });
            });

            // Handle close button
            const closeButton = document.querySelector('.mobile-menu__close-btn');
            if (closeButton) {
                closeButton.addEventListener('click', function() {
                    if (isAnimating) return;

                    // Start slide out animation
                    const mobileMenu = document.querySelector('.mobile-menu');
                    if (mobileMenu) {
                        removeClass(mobileMenu, 'mobile-menu--visible');
                        removeClass(document.body, 'mobile-menu-open');

                        // Hide menu after animation completes
                        setTimeout(() => {
                            mobileMenu.style.display = 'none';

                            // Reset to level 1 for next time menu is opened
                            isAnimating = true;
                            const menuLevels = document.querySelectorAll('.mobile-menu__level');

                            menuLevels.forEach(level => {
                                addClass(level, 'mobile-menu__level--no-transition');
                                removeClass(level, 'mobile-menu__level--active');
                                level.style.transform = 'translateX(100%)';
                            });

                            const level1 = document.querySelector('#level1');
                            if (level1) {
                                addClass(level1, 'mobile-menu__level--active');
                                level1.style.transform = 'translateX(0)';
                            }

                            setTimeout(() => {
                                menuLevels.forEach(level => {
                                    removeClass(level, 'mobile-menu__level--no-transition');
                                });
                                isAnimating = false;
                            }, 50);

                            currentLevel = 1;
                            levelHistory = [{level: 1, id: 'level1'}];

                            // Clear search
                            const searchInput = document.querySelector('.mobile-menu__search-input');
                            if (searchInput) {
                                searchInput.value = '';
                            }
                        }, 300); // Wait for slide animation to complete

                        console.log('Menu closed');
                    }
                });
            }

            // Keyboard navigation support
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const closeBtn = document.querySelector('.mobile-menu__close-btn');
                    if (closeBtn) closeBtn.click();
                }
            });

            // Touch event handling for better mobile experience
            let touchStartX = 0;
            let touchEndX = 0;

            const menuContainer = document.querySelector('.mobile-menu__container');
            if (menuContainer) {
                menuContainer.addEventListener('touchstart', function(e) {
                    touchStartX = e.changedTouches[0].screenX;
                });

                menuContainer.addEventListener('touchend', function(e) {
                    touchEndX = e.changedTouches[0].screenX;
                    handleSwipe();
                });
            }

            function handleSwipe() {
                if (isAnimating) return;

                const swipeThreshold = 50;
                const swipeDistance = touchEndX - touchStartX;

                if (Math.abs(swipeDistance) > swipeThreshold) {
                    if (swipeDistance > 0 && currentLevel > 1 && levelHistory.length > 1) {
                        // Swipe right - go back
                        const backBtn = document.querySelector('.mobile-menu__back-btn');
                        if (backBtn) backBtn.click();
                    }
                    // Swipe left functionality can be added here if needed
                }
            }

            // Accessibility improvements
            const accessibilityItems = document.querySelectorAll('.mobile-menu__item, .mobile-menu__submenu-item');
            accessibilityItems.forEach(item => {
                item.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.click();
                    }
                });
            });

            // Focus management
            const menuLevels = document.querySelectorAll('.mobile-menu__level');
            menuLevels.forEach(level => {
                level.addEventListener('transitionend', function() {
                    if (hasClass(this, 'mobile-menu__level--active')) {
                        // Focus first focusable element in the active level
                        const focusableElement = this.querySelector('.mobile-menu__item, .mobile-menu__submenu-item, .mobile-menu__back-btn');
                        if (focusableElement) {
                            focusableElement.focus();
                        }
                    }
                });
            });

        });
    </script>
</body>
</html>