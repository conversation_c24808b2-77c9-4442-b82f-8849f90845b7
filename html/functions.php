<?php

function add_mobile_menu_location() {
  $existing_menus = get_registered_nav_menus();
  $new_menus = array_merge($existing_menus, array(
    'mobile-menu' => esc_html__('Mobile Menu', 'Divi')
  ));
  register_nav_menus($new_menus);
}
add_action('after_setup_theme', 'add_mobile_menu_location', 20);



/**
 * Add custom fields to menu items for icons
 */
add_action( 'wp_nav_menu_item_custom_fields', 'add_menu_item_icon_field', 10, 4 );
function add_menu_item_icon_field( $item_id, $item, $depth, $args ) {
  $icon_class = get_post_meta( $item_id, '_menu_item_icon', true );
  ?>
    <p class="field-icon description description-wide">
        <label for="edit-menu-item-icon-<?php echo $item_id; ?>">
            Icon Class<br />
            <input type="text" id="edit-menu-item-icon-<?php echo $item_id; ?>" class="widefat code edit-menu-item-icon" name="menu-item-icon[<?php echo $item_id; ?>]" value="<?php echo esc_attr( $icon_class ); ?>" placeholder="e.g., fas fa-laptop" />
        </label>
        <span class="description">Enter Font Awesome icon class (e.g., "fas fa-laptop", "fas fa-cogs")</span>
    </p>
  <?php
}

/**
 * Save custom menu item fields
 */
add_action( 'wp_update_nav_menu_item', 'save_menu_item_icon_field', 10, 3 );
function save_menu_item_icon_field( $menu_id, $menu_item_db_id, $args ) {
  if ( isset( $_POST['menu-item-icon'][ $menu_item_db_id ] ) ) {
    $icon_value = sanitize_text_field( $_POST['menu-item-icon'][ $menu_item_db_id ] );
    update_post_meta( $menu_item_db_id, '_menu_item_icon', $icon_value );
  } else {
    delete_post_meta( $menu_item_db_id, '_menu_item_icon' );
  }
}

/**
 * Custom WordPress Walker for Mobile Menu
 */
class Mobile_Menu_Walker extends Walker_Nav_Menu {

  private $menu_items_by_parent = array();
  private $current_item = null;

  /**
   * Start the list before the elements are added
   */
  public function start_lvl( &$output, $depth = 0, $args = null ) {
    // We handle level structure in the main output, so this is intentionally empty
  }

  /**
   * End the list after the elements are added
   */
  public function end_lvl( &$output, $depth = 0, $args = null ) {
    // We handle level structure in the main output, so this is intentionally empty
  }

  /**
   * Start the element output
   */
  public function start_el( &$output, $item, $depth = 0, $args = null, $id = 0 ) {
    // This method is intentionally empty as we handle all output in the main display method
  }

  /**
   * End the element output
   */
  public function end_el( &$output, $item, $depth = 0, $args = null ) {
    // This method is intentionally empty as we handle all output in the main display method
  }

  /**
   * Display the menu
   */
  public function walk( $elements, $max_depth, ...$args ) {
    $this->organize_menu_items( $elements );

    $output = '';

    // Generate the complete mobile menu structure
    $output .= $this->generate_mobile_menu_header();
    $output .= '<div class="mobile-menu__container">';
    $output .= $this->generate_level_1( $elements );
    $output .= $this->generate_level_2( $elements );
    $output .= $this->generate_region_menu();
    $output .= $this->generate_level_3( $elements );
    $output .= '</div>';
    $output .= $this->generate_mobile_menu_footer();

    return $output;
  }

  /**
   * Print Region menu template
   */
  private function generate_region_menu() {
    return '<!-- Level 2 - Regions Menu -->
                	<div class="mobile-menu__level mobile-menu__level--level-2" id="level2-regions">
                    	<div class="mobile-menu__level-header">
                        	<button class="mobile-menu__back-btn" aria-label="Back to main menu">
                            	<i class="fas fa-chevron-left"></i>
                            </button>
                            <div class="mobile-menu__title">
                            	<i class="fas fa-globe mobile-menu__title-icon"></i>
							</div>
                        </div>
                    	<div class="mobile-menu__content"></div>
                  	</div>';
  }


  /**
   * Organize menu items by parent ID
   */
  private function organize_menu_items( $menu_items ) {
    $this->menu_items_by_parent = array();

    foreach ( $menu_items as $item ) {
      $parent_id = (int) $item->menu_item_parent;
      if ( ! isset( $this->menu_items_by_parent[ $parent_id ] ) ) {
        $this->menu_items_by_parent[ $parent_id ] = array();
      }
      $this->menu_items_by_parent[ $parent_id ][] = $item;
    }
  }

  /**
   * Generate mobile menu header
   */
  private function generate_mobile_menu_header() {
    $login_url = '/login/';

    $output = '<!-- Menu Header -->';
    $output .= '<div class="mobile-menu__header">';
    $output .= '<div class="mobile-menu__status-icons">';
    $output .= '<a href="' . esc_url( $login_url ) . '">';
    $output .= '<i class="fa fa-user-circle"></i>';
    $output .= '</a>';
    $output .= '<i class="fa-regular fa-globe mobile-menu__status-icon-region"></i>';
    $output .= '</div>';
    /*
    $output .= '<button class="mobile-menu__close-btn" aria-label="Close menu">';
    $output .= '<i class="fas fa-times"></i>';
    $output .= '</button>';
*/

    $output .= '<div class="hamburger"><a class="main-nav-toggle" href="#main-nav"><i>Menu</i></a></div>';

    $output .= '</div>';

    return $output;
  }

  /**
   * Generate mobile menu footer
   */
  private function generate_mobile_menu_footer() {

    $output = '<!-- Single shared footer for all menu levels -->';
    $output .= '<a href="/command-cloud/" class="mobile-menu__footer-link">';
    $output .= 'Learn About Command Cloud';
    $output .= '<i class="fas fa-chevron-right"></i>';
    $output .= '</a>';

    // Add the menu gradient.
    $output .= '<div class="bottom-gradient"></div>';

    return $output;
  }

  /**
   * Generate search form
   */
  private function generate_search_form() {
    $search_url = home_url( '/' );

    $output = '<!-- Search -->';
    $output .= '<form class="mobile-menu__search" method="GET" action="' . esc_url( $search_url ) . '">';
    $output .= '<input type="text" class="mobile-menu__search-input" placeholder="Search" name="s" aria-label="Search">';
    $output .= '<input type="hidden" name="et_pb_searchform_submit" value="">';
    $output .= '<input type="hidden" name="et_pb_include_posts" value="yes">';
    $output .= '<input type="hidden" name="et_pb_include_pages" value="yes">';
    $output .= '<button type="submit" class="mobile-menu__search-btn" aria-label="Search">';
    $output .= '<i class="fas fa-search mobile-menu__search-icon"></i>';
    $output .= '</button>';
    $output .= '</form>';

    return $output;
  }

  /**
   * Generate Level 1 menu
   */
  private function generate_level_1( $elements ) {
    $output = '';
    $output .= '<!-- Level 1 - Main Menu -->';
    $output .= '<div class="mobile-menu__level mobile-menu__level--level-1 mobile-menu__level--active" id="level1">';

    // Add search form
    $output .= $this->generate_search_form();

    // Menu content
    $output .= '<div class="mobile-menu__content">';

    if ( isset( $this->menu_items_by_parent[0] ) ) {
      foreach ( $this->menu_items_by_parent[0] as $item ) {
        $icon_class = get_post_meta( $item->ID, '_menu_item_icon', true );
        if ( empty( $icon_class ) ) {
          $icon_class = $this->get_fallback_icon( $item->title );
        }

        $has_children = isset( $this->menu_items_by_parent[ $item->ID ] );
        $data_target = $has_children ? 'data-target="' . sanitize_title( $item->title ) . '"' : '';
        $aria_haspopup = $has_children ? 'aria-haspopup="true"' : '';

        $output .= '<a href="' . esc_url( $item->url ) . '" class="mobile-menu__item" ' . $data_target . ' role="menuitem" ' . $aria_haspopup . '>';
        $output .= '<i class="' . esc_attr( $icon_class ) . ' mobile-menu__item-icon"></i>';
        $output .= '<span class="mobile-menu__item-text">' . esc_html( $item->title ) . '</span>';
        $output .= '<i class="fas fa-chevron-right mobile-menu__item-arrow"></i>';

        $output .= '</a>';
      }
    }

    $output .= '</div>'; // End menu-content
    $output .= '</div>'; // End level1

    return $output;
  }

  /**
   * Generate Level 2 menus
   */
  private function generate_level_2( $elements ) {
    $output = '';

    if ( isset( $this->menu_items_by_parent[0] ) ) {
      foreach ( $this->menu_items_by_parent[0] as $parent_item ) {
        if ( isset( $this->menu_items_by_parent[ $parent_item->ID ] ) ) {
          $target_id = 'level2-' . sanitize_title( $parent_item->title );

          $icon_class = get_post_meta( $parent_item->ID, '_menu_item_icon', true );
          if ( empty( $icon_class ) ) {
            $icon_class = $this->get_fallback_icon( $parent_item->title );
          }

          $output .= '<!-- Level 2 - ' . esc_html( $parent_item->title ) . ' Menu -->';
          $output .= '<div class="mobile-menu__level mobile-menu__level--level-2" id="' . esc_attr( $target_id ) . '">';

          // Level header
          $output .= '<div class="mobile-menu__level-header">';
          $output .= '<button class="mobile-menu__back-btn" aria-label="Back to main menu">';
          $output .= '<i class="fas fa-chevron-left"></i>';
          $output .= '</button>';
          $output .= '<div class="mobile-menu__title">';
          $output .= '<i class="' . esc_attr( $icon_class ) . ' mobile-menu__title-icon"></i>';
          $output .= esc_html( $parent_item->title );
          $output .= '</div>';
          $output .= '</div>';

          // Menu content
          $output .= '<div class="mobile-menu__content">';

          foreach ( $this->menu_items_by_parent[ $parent_item->ID ] as $child_item ) {
            $has_children = isset( $this->menu_items_by_parent[ $child_item->ID ] );
            $data_target = $has_children ? 'data-target="' . sanitize_title( $child_item->title ) . '"' : '';
            $aria_haspopup = $has_children ? 'aria-haspopup="true"' : '';

            $output .= '<a href="' . esc_url( $child_item->url ) . '" class="mobile-menu__submenu-item" ' . $data_target . ' role="menuitem" ' . $aria_haspopup . '>';
            $output .= '<span class="mobile-menu__submenu-item-text">' . esc_html( $child_item->title ) . '</span>';

            if ( $has_children ) {
              $output .= '<i class="fas fa-chevron-right mobile-menu__submenu-item-arrow"></i>';
            }

            $output .= '</a>';
          }

          $output .= '</div>'; // End menu-content
          $output .= '</div>'; // End level2
        }
      }
    }

    return $output;
  }

  /**
   * Generate Level 3 menus
   */
  private function generate_level_3( $elements ) {
    $output = '';

    if ( isset( $this->menu_items_by_parent[0] ) ) {
      foreach ( $this->menu_items_by_parent[0] as $parent_item ) {
        if ( isset( $this->menu_items_by_parent[ $parent_item->ID ] ) ) {
          foreach ( $this->menu_items_by_parent[ $parent_item->ID ] as $child_item ) {
            if ( isset( $this->menu_items_by_parent[ $child_item->ID ] ) ) {
              $target_id = 'level3-' . sanitize_title( $child_item->title );

              $output .= '<!-- Level 3 - ' . esc_html( $child_item->title ) . ' Menu -->';
              $output .= '<div class="mobile-menu__level mobile-menu__level--level-3" id="' . esc_attr( $target_id ) . '">';

              // Level header
              $output .= '<div class="mobile-menu__level-header">';
              $output .= '<button class="mobile-menu__back-btn" aria-label="Back to previous menu">';
              $output .= '<i class="fas fa-chevron-left"></i>';
              $output .= '</button>';
              $output .= '<div class="mobile-menu__title">';
              $output .= esc_html( $child_item->title );
              $output .= '</div>';
              $output .= '</div>';

              // Menu content
              $output .= '<div class="mobile-menu__content">';

              foreach ( $this->menu_items_by_parent[ $child_item->ID ] as $grandchild_item ) {
                $output .= '<a href="' . esc_url( $grandchild_item->url ) . '" class="mobile-menu__submenu-item" role="menuitem">';
                $output .= '<span class="mobile-menu__submenu-item-text">' . esc_html( $grandchild_item->title ) . '</span>';
                $output .= '</a>';
              }

              $output .= '</div>'; // End menu-content
              $output .= '</div>'; // End level3
            }
          }
        }
      }
    }

    return $output;
  }

  /**
   * Get fallback icon based on menu item title
   */
  private function get_fallback_icon( $title ) {
    $title_lower = strtolower( $title );

    $icon_map = array(
      'products' => 'fas fa-laptop',
      'services' => 'fa-regular fa-gear',
      'resources' => 'fa-regular fa-book',
      'partners' => 'fa-regular fa-handshake',
      'overview' => 'fas fa-chart-bar',
      'events' => 'fa-regular fa-location-dot',
      'contact' => 'fa-regular fa-messages',
      'regions' => 'fa-regular fa-globe',
      'about' => 'fas fa-info-circle',
      'news' => 'fas fa-newspaper',
      'blog' => 'fas fa-blog',
      'support' => 'fas fa-life-ring',
      'solutions' => 'fas fa-lightbulb',
      'technology' => 'fas fa-microchip',
      'industries' => 'fas fa-industry',
      'careers' => 'fas fa-briefcase',
    );

    foreach ( $icon_map as $keyword => $icon ) {
      if ( strpos( $title_lower, $keyword ) !== false ) {
        return $icon;
      }
    }

    return 'fas fa-circle'; // Default fallback icon
  }
}

/**
 * Helper function to display mobile menu in templates
 * Usage: echo display_mobile_menu();
 */
function display_mobile_menu() {
  if ( has_nav_menu( 'mobile-menu' ) ) {
    wp_nav_menu( array(
      'theme_location' => 'mobile-menu',
      'walker' => new Mobile_Menu_Walker(),
      'container' => false,
      'menu_class' => 'mobile-menu',
      'fallback_cb' => false
    ) );
  } else {
    echo '<!-- Mobile menu location not set or no menu assigned -->';
  }
}

/**
 * Shortcode to display mobile menu
 * Usage: [mobile_menu]
 */
add_shortcode( 'mobile_menu', 'mobile_menu_shortcode' );
function mobile_menu_shortcode( $atts ) {
  ob_start();
  display_mobile_menu();
  return ob_get_clean();
}

function custom_mobile_navigation() { ?>
    <style type="text/css">
        /* OVERWRITE ET BUILDER STYLES */

        .mobile_nav.closed {
            display: none !important;
        }

        #et_mobile_nav_menu .select_page {
            display: none;
        }

        @media all and (max-width: 980px) {
            #main-header {
                top: 0px !important;
                position: fixed !important;
                background-color: #ffffff !important;
                box-shadow:  none !important;
                line-height: 23px;
                font-weight: 500;
                width: 100%;
                z-index: 999999;
                height: 60px;
                display: flex;
                flex-direction: column;
            }
            #page-container {
                padding-top: 60px !important;
            }
            #et-top-navigation {
                padding-top: 17px !important;
                padding-bottom: 17px !important;
            }
        }

        /* MOBILE MENU STYLES */

        .mobile-menu {
            position: fixed;
            top: 0;
            left: 100%;
            width: 100%;
            height: 100vh;
            background-color: #fff;
            overflow: hidden;
            z-index: 9999;
            display: none;
            transition: transform 0.3s ease-out;
        }

        .mobile-menu--visible {
            display: block;
        }

        /* Prevent body scroll when menu is open and slide background content */
        body.mobile-menu-open {
            overflow: hidden;
        }


        /* Slide background content to the left when menu is open */
        body.mobile-menu-open #et-main-area footer div.et_pb_section.et_pb_section_2_tb_footer,
        body.mobile-menu-open #main-content,
        body.mobile-menu-open #main-header{
            transform: translateX(-100%);
            transition: transform 0.3s ease-out !important;
        }

        body.mobile-menu-open #et-main-area footer div.et_pb_section.et_pb_section_3_tb_footer {
            transform: translateX(-150%);
            transition: transform 0.3s ease-out !important;
        }

        /* Reset background content position when menu is closed */
        body:not(.mobile-menu-open) #et-main-area footer div.et_pb_section.et_pb_section_2_tb_footer,
        body:not(.mobile-menu-open) #main-content,
        body:not(.mobile-menu-open) #main-header{
            transform: translateX(0);
            transition: transform 0.3s ease-out !important;
        }

        body:not(.mobile-menu-open) #et-main-area footer div.et_pb_section.et_pb_section_3_tb_footer {
            transform: translateX(-50%);
            transition: transform 0.3s ease-out !important;
        }

        .mobile-menu__button {
            color: #08243E;
            padding: 3px;
            display: flex;
            background: none;
            border: none;
            font-size: 20px;
            margin-left: auto;
            position: relative;
        }

        .mobile-menu__header {
            background-color: #08243E;
            color: white;
            padding: 0 25px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: 60px;
        }

        .mobile-menu__status-icons {
            display: flex;
            align-items: center;
            margin-right: 40px;
            gap: 18px;
        }

        .mobile-menu__status-icons a {
            color: inherit;
            line-height: 0;
            text-decoration: none;
        }

        .mobile-menu__status-icons i {
            font-size: 20px;
            cursor: pointer;
        }

        .mobile-menu__close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
        }

        .mobile-menu__container {
            position: relative;
            height: calc(100vh - 60px - 85px); /* Subtract header height and footer height from viewport height */
            overflow-y: auto;
            overflow-x: hidden;
        }

        .mobile-menu__level {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #fff;
            display: flex;
            flex-direction: column;
            transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            will-change: transform;
        }

        /* Initial positions */
        .mobile-menu__level {
            transform: translateX(100%);
        }

        .mobile-menu__level--active {
            transform: translateX(0);
        }

        /* Animation states */
        .mobile-menu__level--slide-out-left {
            transform: translateX(-100%);
        }

        .mobile-menu__level--slide-out-right {
            transform: translateX(100%);
        }

        .mobile-menu__level--slide-in-from-right {
            transform: translateX(0);
        }

        .mobile-menu__level--slide-in-from-left {
            transform: translateX(0);
        }

        .mobile-menu__search {
            padding: 39px 23px;
            background-color: #f8f9fa;
            position: relative;
        }

        .mobile-menu__search-input {
            width: 100%;
            height: 40px;
            padding: 12px 56px 12px 24px !important;
            border: none !important;
            border-radius: 25px;
            background-color: #E9E9E9 !important;
            font-size: 12px;
            outline: none;
        }

        .mobile-menu__search-btn {
            position: absolute;
            right: 50px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            padding: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mobile-menu__search-icon {
            color: #000;
            font-size: 16px;
        }

        .mobile-menu__content {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 10px; /* Add some padding to prevent content from touching the footer */
        }

        .mobile-menu__item {
            display: flex;
            align-items: center;
            padding: 16px 23px;
            height: 70px;
            border-bottom: 1px solid #C1C1C1;
            cursor: pointer;
            text-decoration: none;
            color: #000;
        }

        .mobile-menu__item-icon {
            margin-right: 10px;
            font-size: 18px;
            color: #000;
            width: 24px;
            text-align: center;
        }

        .mobile-menu__item-text {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
        }

        .mobile-menu__item-arrow {
            color: #000;
            font-size: 14px;
        }

        .mobile-menu__level-header {
            display: flex;
            align-items: center;
            padding: 20px;
            height: 104px;
            border-bottom: 1px solid #C1C1C1;
            background-color: #fff;
        }

        .mobile-menu__back-btn {
            background: none;
            border: none;
            font-size: 18px;
            color: #000;
            cursor: pointer;
            margin-right: 15px;
            padding: 5px;
        }

        .mobile-menu__title {
            font-size: 20px;
            font-weight: 600;
            color: #000;
            display: flex;
            align-items: center;
        }

        .mobile-menu__title-icon {
            margin-right: 12px;
            font-size: 18px;
        }

        /* Base */
        .mobile-menu__footer-link {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: #1e3a5f linear-gradient(179.34deg, #002856 -78.97%, #00AAFF 196.03%);
            padding: 24px;
            height: 85px;
            display: flex;
            z-index: 101;
            color: #fff !important;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            align-items: center;
            justify-content: space-between;

            /* iOS Safari: kill tap + long-press visuals */
            -webkit-tap-highlight-color: rgba(0,0,0,0);
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
            -webkit-appearance: none;
            appearance: none;

            /* kill any focus ring/box */
            outline: none !important;
            outline-color: transparent !important;
            box-shadow: none !important;
            border: none; /* in case reset adds one */
        }

        /* All states remain identical */
        .mobile-menu__footer-link:link,
        .mobile-menu__footer-link:visited,
        .mobile-menu__footer-link:hover,
        .mobile-menu__footer-link:active,
        .mobile-menu__footer-link:focus,
        .mobile-menu__footer-link:focus-visible,
        .mobile-menu__footer-link:focus-within {
            color: #fff !important;
            background: #1e3a5f linear-gradient(179.34deg, #002856 -78.97%, #00AAFF 196.03%);
            text-decoration: none;
            outline: none !important;
            box-shadow: none !important;
        }

        /* If there's an <a> inside, keep it identical too */
        .mobile-menu__footer-link a,
        .mobile-menu__footer-link a:link,
        .mobile-menu__footer-link a:visited,
        .mobile-menu__footer-link a:hover,
        .mobile-menu__footer-link a:active,
        .mobile-menu__footer-link a:focus,
        .mobile-menu__footer-link a:focus-visible {
            color: #fff !important;
            text-decoration: none;
            outline: none !important;
            box-shadow: none !important;
            -webkit-tap-highlight-color: rgba(0,0,0,0);
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
            -webkit-appearance: none;
            appearance: none;
        }





        .mobile-menu__submenu-item {
            display: flex;
            align-items: center;
            padding: 16px 27px;
            height: 70px;
            border-bottom: 1px solid #C1C1C1;
            cursor: pointer;
            text-decoration: none;
            color: #000;
        }

        .mobile-menu__submenu-item-text {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
        }

        .mobile-menu__submenu-item-arrow {
            color: #000;
            font-size: 14px;
        }

        /* Disable transitions during setup */
        .mobile-menu__level--no-transition {
            transition: none !important;
        }

        /* Ensure smooth animations */
        .mobile-menu__level--animating {
            pointer-events: none;
        }

        /* Create bottom fade so user knows they can scroll. */
        .bottom-gradient {
            position: absolute;
            bottom: 85px;
            height: 50px;
            background: linear-gradient(rgba(255, 255, 255, 0.0), #FFF);
            width: 100%;
            z-index: 100;
            pointer-events: none;
        }

        /* Animated hamburger menu. */
        .hamburger {
            display: inline-block;
            height: 12px;
            position: relative;
        }
        .hamburger a.main-nav-toggle {
            display: block;
            width: 16px;
            height: 12px;
            position: static;
        }
        .hamburger a.main-nav-toggle:after, .hamburger a.main-nav-toggle:before {
            content: '';
            position: absolute;
            top: 0;
            height: 0;
            border-bottom: 2px solid #FFF;
            width: 100%;
            left: 0;
            right: 0;
            transition: all ease-out 0.3s;
        }
        .hamburger a.main-nav-toggle:after {
            top: 100%;
        }
        .hamburger a.main-nav-toggle i {
            display: block;
            text-indent: 100%;
            overflow: hidden;
            white-space: nowrap;
            height: 2px;
            background-color: #FFF;
            width: 100%;
            position: absolute;
            top: 50%;
            transition: all ease-out 0.1s;
        }
        .hamburger a.main-nav-toggle.active-menu:after {
            transform: rotate(-45deg);
            transform-origin: center;
            top: 50%;
        }
        .hamburger a.main-nav-toggle.active-menu:before {
            transform: rotate(45deg);
            transform-origin: center;
            top: 50%;
        }
        .hamburger a.main-nav-toggle.active-menu i {
            opacity: 0;
        }

        /* MEDIA QUERIES START HERE */

        @supports (height: 100dvh) {
            .mobile-menu {
                height: 100dvh;
            }

            .mobile-menu__container {
                height: calc(100dvh - 60px - 85px); /* Subtract header height and footer height from viewport height */
            }
        }

        /* Use container queries in modern browsers */
        @supports (container-type: size) {
            .mobile-menu {
                container-type: size;
                container-name: mobileMenu;
            }
        }

        @media all and (max-height: 755px) {
            .mobile-menu__search {
                padding: 23px;
            }
        }

        /* Use container queries in modern browsers */
        @supports (container-type: size) {
            @container mobileMenu (height < 755px) {
                .mobile-menu__search {
                    padding: 23px;
                }
            }
        }

        @media all and (max-height: 730px) {
            .mobile-menu__item {
                padding: 6px 23px;
                height: 60px;
            }
        }

        @supports (container-type: size) {
            @container mobileMenu (height < 730px) {
                .mobile-menu__item {
                    padding: 6px 23px;
                    height: 60px;
                }
            }
        }

        @media all and (max-height: 645px) {
            .mobile-menu__item {
                height: 50px;
            }
            .mobile-menu__item-text {
                font-size: 14px;
            }
        }

        @supports (container-type: size) {
            @container mobileMenu (height < 645px) {
                .mobile-menu__item {
                    height: 50px;
                }
                .mobile-menu__item-text {
                    font-size: 14px;
                }
            }
        }

        @media all and (max-height: 590px) {

            .bottom-gradient {
                bottom: 50px;
            }

            .mobile-menu__footer-link {
                height: 50px;
            }

            .mobile-menu__container {
                height: calc(100vh - 60px - 50px); /* Subtract header height and footer height from viewport height */
            }

            @supports (height: 100dvh) {
                .mobile-menu__container {
                    height: calc(100dvh - 60px - 50px); /* Subtract header height and footer height from viewport height */
                }
            }
        }

        @supports (container-type: size) {
            @container mobileMenu (height < 590px) {

                .bottom-gradient {
                    bottom: 50px;
                }

                .mobile-menu__footer-link {
                    height: 50px;
                }

                .mobile-menu__container {
                    height: calc(100vh - 60px - 50px); /* Subtract header height and footer height from viewport height */
                }

                @supports (height: 100dvh) {
                    .mobile-menu__container {
                        height: calc(100dvh - 60px - 50px); /* Subtract header height and footer height from viewport height */
                    }
                }
            }
        }
    </style>

  <?php
  if ( has_nav_menu( 'mobile-menu' ) && (is_customize_preview() || ('slide' !== et_get_option('header_style', 'left') && 'fullscreen' !== et_get_option('header_style', 'left'))) ) {
    ?>
      <div id="et_mobile_nav_menu">
          <span class="select_page"><?php esc_html_e('Select Page', 'Divi'); ?></span>
          <button class="mobile-menu__button">
              <i class="fas fa-bars"></i>
          </button>
        <?php display_mobile_menu(); ?>
      </div>

      <script type="text/javascript">
          let currentLevel = 1;
          let levelHistory = [{level: 1, id: 'level1'}];
          let isAnimating = false;

          // Helper functions for DOM manipulation

          const addClass = (element, className) => {
              if (element) element.classList.add(className);
          };

          const removeClass = (element, className) => {
              if (element) element.classList.remove(className);
          };

          const toggleClass = (element, className) => {
              if (element) element.classList.toggle(className);
          };

          const hasClass = (element, className) => {
              return element ? element.classList.contains(className) : false;
          };

          // Initialize menu - set level1 as active, others hidden
          function initializeMenu() {
              const menuLevels = document.querySelectorAll('.mobile-menu__level');

              menuLevels.forEach(level => {
                  removeClass(level, 'mobile-menu__level--active');
                  removeClass(level, 'mobile-menu__level--no-transition');
                  addClass(level, 'mobile-menu__level--no-transition');
              });

              // Set initial positions without animation
              const level1 = document.querySelector('#level1');
              if (level1) {
                  level1.style.transform = 'translateX(0)';
                  addClass(level1, 'mobile-menu__level--active');
              }

              menuLevels.forEach(level => {
                  if (level.id !== 'level1') {
                      level.style.transform = 'translateX(100%)';
                  }
              });

              // Re-enable transitions after a brief delay
              setTimeout(() => {
                  menuLevels.forEach(level => {
                      removeClass(level, 'mobile-menu__level--no-transition');
                  });
              }, 50);
          }

          // Menu navigation function with improved animation
          function navigateToLevel(targetLevel, targetId, direction = 'forward') {
              if (isAnimating) return; // Prevent multiple animations

              const currentLevelElement = document.querySelector('.mobile-menu__level--active');
              const targetLevelElement = document.querySelector('#' + targetId);

              if (!targetLevelElement || currentLevelElement?.id === targetId) {
                  return; // Target doesn't exist or is already active
              }

              isAnimating = true;

              // Add animating class to prevent interactions
              const menuLevels = document.querySelectorAll('.mobile-menu__level');
              menuLevels.forEach(level => {
                  addClass(level, 'mobile-menu__level--animating');
              });

              if (direction === 'forward') {
                  // Forward navigation: current slides left, new slides in from right

                  // Position target element off-screen to the right
                  addClass(targetLevelElement, 'mobile-menu__level--no-transition');
                  targetLevelElement.style.transform = 'translateX(100%)';

                  // Re-enable transition and start animation
                  setTimeout(() => {
                      removeClass(targetLevelElement, 'mobile-menu__level--no-transition');

                      // Animate current level out to the left
                      if (currentLevelElement) {
                          currentLevelElement.style.transform = 'translateX(-100%)';
                      }

                      // Animate target level in from the right
                      targetLevelElement.style.transform = 'translateX(0)';

                      // Update active states
                      if (currentLevelElement) {
                          removeClass(currentLevelElement, 'mobile-menu__level--active');
                      }
                      addClass(targetLevelElement, 'mobile-menu__level--active');
                  }, 10);

                  // Add to history
                  levelHistory.push({level: targetLevel, id: targetId});

              } else {
                  // Backward navigation: current slides right, previous slides in from left

                  // Position target element off-screen to the left
                  addClass(targetLevelElement, 'mobile-menu__level--no-transition');
                  targetLevelElement.style.transform = 'translateX(-100%)';

                  // Re-enable transition and start animation
                  setTimeout(() => {
                      removeClass(targetLevelElement, 'mobile-menu__level--no-transition');

                      // Animate current level out to the right
                      if (currentLevelElement) {
                          currentLevelElement.style.transform = 'translateX(100%)';
                      }

                      // Animate target level in from the left
                      targetLevelElement.style.transform = 'translateX(0)';

                      // Update active states
                      if (currentLevelElement) {
                          removeClass(currentLevelElement, 'mobile-menu__level--active');
                      }
                      addClass(targetLevelElement, 'mobile-menu__level--active');
                  }, 10);
              }

              currentLevel = targetLevel;

              // Clean up after animation completes
              setTimeout(() => {
                  isAnimating = false;
                  menuLevels.forEach(level => {
                      removeClass(level, 'mobile-menu__level--animating');
                  });

                  // Reset positions of non-active levels
                  menuLevels.forEach(level => {
                      if (!hasClass(level, 'mobile-menu__level--active')) {
                          addClass(level, 'mobile-menu__level--no-transition');
                          level.style.transform = 'translateX(100%)';
                      }
                  });

                  setTimeout(() => {
                      menuLevels.forEach(level => {
                          removeClass(level, 'mobile-menu__level--no-transition');
                      });
                  }, 10);
              }, 320); // Slightly longer than transition duration
          }

          // Function to dynamically generate region menu items from #et-secondary-nav
          function generateRegionMenuItems() {
              const secondaryNav = document.querySelector('#et-secondary-nav');
              const regionsMenuContent = document.querySelector('#level2-regions .mobile-menu__content');
              const regionsMenuTitle = document.querySelector('#level2-regions .mobile-menu__title');

              if (!secondaryNav || !regionsMenuContent) {
                  console.warn('Secondary nav or regions menu content not found');
                  return;
              }

              // Find the region menu item (contains globe icon or "Region" text)
              const regionMenuItem = Array.from(secondaryNav.querySelectorAll('li.menu-item-has-children')).find(item => {
                  const link = item.querySelector('a');
                  return link && (
                      link.textContent.toLowerCase().includes('region') ||
                      link.querySelector('.fa-globe')
                  );
              });

              if (!regionMenuItem) {
                  console.warn('Region menu item not found in secondary nav');
                  return;
              }

              const subMenu = regionMenuItem.querySelector('.sub-menu');
              if (!subMenu) {
                  console.warn('Region submenu not found');
                  return;
              }

              // Extract the title text from the parent menu item link
              const regionMenuLink = regionMenuItem.querySelector('a');
              if (regionMenuLink && regionsMenuTitle) {
                  // Get the text content, excluding any icon text
                  let titleText = regionMenuLink.textContent.trim();

                  // Remove any icon text that might be included (like from screen readers)
                  titleText = titleText.replace(/^\s*\S+\s+/, '').trim(); // Remove first word if it looks like an icon

                  // If the text still seems to contain icon artifacts, try a more specific approach
                  const iconElement = regionMenuLink.querySelector('i');
                  if (iconElement) {
                      // Clone the link, remove the icon, and get clean text
                      const tempLink = regionMenuLink.cloneNode(true);
                      const tempIcon = tempLink.querySelector('i');
                      if (tempIcon) {
                          tempIcon.remove();
                      }
                      titleText = tempLink.textContent.trim();
                  }

                  // Update the mobile menu title while preserving the icon
                  const titleIcon = regionsMenuTitle.querySelector('.mobile-menu__title-icon');
                  if (titleIcon) {
                      regionsMenuTitle.innerHTML = '';
                      regionsMenuTitle.appendChild(titleIcon);
                      regionsMenuTitle.appendChild(document.createTextNode('\n                                    ' + titleText));
                  } else {
                      // Fallback if icon is not found - preserve any existing icon HTML
                      const iconHTML = regionsMenuTitle.innerHTML.match(/<i[^>]*><\/i>/);
                      if (iconHTML) {
                          regionsMenuTitle.innerHTML = iconHTML[0] + '\n                                    ' + titleText;
                      } else {
                          regionsMenuTitle.textContent = titleText;
                      }
                  }
              }

              // Extract region items from submenu
              const regionItems = subMenu.querySelectorAll('li.menu-item a');

              if (regionItems.length === 0) {
                  console.warn('No region items found in submenu');
                  return;
              }

              // Generate mobile menu HTML structure
              let mobileMenuHTML = '';
              regionItems.forEach(item => {
                  const href = item.getAttribute('href') || '#';
                  const text = item.textContent.trim();

                  mobileMenuHTML += `
	<a href="${href}" class="mobile-menu__submenu-item" role="menuitem">
	<span class="mobile-menu__submenu-item-text">${text}</span>
			</a>`;
              });

              // Replace the content in the regions menu
              regionsMenuContent.innerHTML = mobileMenuHTML;

              console.log('Region menu items generated successfully:', regionItems.length, 'items');
          }

          // Initialize menu on load
          initializeMenu();

          // Generate dynamic region menu items
          generateRegionMenuItems();

          // Handle mobile menu toggle button click (show menu)
          const toggleButton = document.querySelector('.mobile-menu__button');
          if (toggleButton) {
              toggleButton.addEventListener('click', function(e) {
                  e.preventDefault();

                  // Show menu first, then trigger animation
                  const mobileMenu = document.querySelector('.mobile-menu');
                  const hamburgerMenu = document.querySelector('.main-nav-toggle');
                  if (mobileMenu) {
                      mobileMenu.style.display = 'block';
                      addClass(document.body, 'mobile-menu-open');

                      // Trigger animation on next frame
                      requestAnimationFrame(() => {
                          addClass(mobileMenu, 'mobile-menu--visible');
                      });

                      setTimeout(function () {
                          addClass(hamburgerMenu, 'active-menu');
                      }, 500);

                      console.log('Menu opened');
                  }
              });
          }

          // Handle clicking outside menu to close it
          const mobileMenu = document.querySelector('.mobile-menu');
          if (mobileMenu) {
              mobileMenu.addEventListener('click', function(e) {
                  // Only close if clicking on the backdrop (the menu itself, not its children)
                  if (e.target === this) {
                      // const closeBtn = document.querySelector('.mobile-menu__close-btn');
                      const closeBtn = document.querySelector('.hamburger');
                      if (closeBtn) closeBtn.click();
                  }
              });
          }

          // Handle globe icon click to navigate to regions menu
          const globeIcon = document.querySelector('.mobile-menu__status-icons .fa-globe');
          if (globeIcon) {
              globeIcon.addEventListener('click', function(e) {
                  e.preventDefault();
                  const regionsLevel = document.querySelector('#level2-regions');
                  if (!isAnimating && regionsLevel) {
                      navigateToLevel(2, 'level2-regions', 'forward');
                      console.log('Navigated to regions menu');
                  }
              });
          }

          // Handle main menu item clicks
          const menuItems = document.querySelectorAll('.mobile-menu__item[data-target]');
          menuItems.forEach(item => {
              item.addEventListener('click', function(e) {
                  e.preventDefault();
                  const target = this.getAttribute('data-target');
                  const targetId = 'level2-' + target;

                  if (document.querySelector('#' + targetId)) {
                      navigateToLevel(2, targetId, 'forward');
                  }
              });
          });

          // Handle submenu item clicks for level 3
          const submenuItems = document.querySelectorAll('.mobile-menu__submenu-item[data-target]');
          submenuItems.forEach(item => {
              item.addEventListener('click', function(e) {
                  e.preventDefault();
                  const target = this.getAttribute('data-target');
                  const targetId = 'level3-' + target;

                  if (document.querySelector('#' + targetId)) {
                      navigateToLevel(3, targetId, 'forward');
                  }
              });
          });

          // Handle back button clicks
          const backButtons = document.querySelectorAll('.mobile-menu__back-btn');
          backButtons.forEach(button => {
              button.addEventListener('click', function(e) {
                  e.preventDefault();

                  if (isAnimating || levelHistory.length <= 1) return;

                  // Remove current level from history
                  levelHistory.pop();

                  // Get previous level info
                  const previousLevelInfo = levelHistory[levelHistory.length - 1];

                  if (previousLevelInfo) {
                      navigateToLevel(previousLevelInfo.level, previousLevelInfo.id, 'backward');
                  }
              });
          });

          // Handle close button
          // const closeButton = document.querySelector('.mobile-menu__close-btn');
          const closeButton = document.querySelector('.hamburger');
          const closeButtonIcon = document.querySelector('.main-nav-toggle');
          if (closeButton) {
              closeButton.addEventListener('click', function() {
                  if (isAnimating) return;

                  // Start slide out animation
                  const mobileMenu = document.querySelector('.mobile-menu');
                  if (mobileMenu) {
                      removeClass(mobileMenu, 'mobile-menu--visible');
                      removeClass(document.body, 'mobile-menu-open');

                      // Hide menu after animation completes
                      setTimeout(() => {
                          mobileMenu.style.display = 'none';

                          // Reset to level 1 for next time menu is opened
                          isAnimating = true;
                          const menuLevels = document.querySelectorAll('.mobile-menu__level');

                          menuLevels.forEach(level => {
                              addClass(level, 'mobile-menu__level--no-transition');
                              removeClass(level, 'mobile-menu__level--active');
                              level.style.transform = 'translateX(100%)';
                          });

                          const level1 = document.querySelector('#level1');
                          if (level1) {
                              addClass(level1, 'mobile-menu__level--active');
                              level1.style.transform = 'translateX(0)';
                          }

                          setTimeout(() => {
                              menuLevels.forEach(level => {
                                  removeClass(level, 'mobile-menu__level--no-transition');
                              });
                              isAnimating = false;
                          }, 50);

                          currentLevel = 1;
                          levelHistory = [{level: 1, id: 'level1'}];

                          // Clear search
                          const searchInput = document.querySelector('.mobile-menu__search-input');
                          if (searchInput) {
                              searchInput.value = '';
                          }

                          removeClass(closeButtonIcon, 'active-menu');

                      }, 300); // Wait for slide animation to complete

                      console.log('Menu closed');



                  }
              });
          }

          // Keyboard navigation support
          document.addEventListener('keydown', function(e) {
              if (e.key === 'Escape') {
                  const closeBtn = document.querySelector('.mobile-menu__close-btn');
                  if (closeBtn) closeBtn.click();
              }
          });

          // Touch event handling for better mobile experience
          let touchStartX = 0;
          let touchEndX = 0;

          const menuContainer = document.querySelector('.mobile-menu__container');
          if (menuContainer) {
              menuContainer.addEventListener('touchstart', function(e) {
                  touchStartX = e.changedTouches[0].screenX;
              });

              menuContainer.addEventListener('touchend', function(e) {
                  touchEndX = e.changedTouches[0].screenX;
                  handleSwipe();
              });
          }

          function handleSwipe() {
              if (isAnimating) return;

              const swipeThreshold = 50;
              const swipeDistance = touchEndX - touchStartX;

              if (Math.abs(swipeDistance) > swipeThreshold) {
                  if (swipeDistance > 0 && currentLevel > 1 && levelHistory.length > 1) {
                      // Swipe right - go back
                      const backBtn = document.querySelector('.mobile-menu__back-btn');
                      if (backBtn) backBtn.click();
                  }
                  // Swipe left functionality can be added here if needed
              }
          }

          // Accessibility improvements
          const accessibilityItems = document.querySelectorAll('.mobile-menu__item, .mobile-menu__submenu-item');
          accessibilityItems.forEach(item => {
              item.addEventListener('keydown', function(e) {
                  if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      this.click();
                  }
              });
          });

          // Focus management
          const menuLevels = document.querySelectorAll('.mobile-menu__level');
          menuLevels.forEach(level => {
              level.addEventListener('transitionend', function() {
                  if (hasClass(this, 'mobile-menu__level--active')) {
                      // Focus first focusable element in the active level
                      const focusableElement = this.querySelector('.mobile-menu__item, .mobile-menu__submenu-item, .mobile-menu__back-btn');
                      if (focusableElement) {
                          focusableElement.focus();
                      }
                  }
              });
          });
      </script>
    <?php
  }
}

function replace_divi_mobile_navigation() {
  remove_action('et_header_top', 'et_add_mobile_navigation');
  add_action('et_header_top', 'custom_mobile_navigation');
}
add_action('init', 'replace_divi_mobile_navigation');

/*
 * USAGE INSTRUCTIONS:
 *
 * 1. In your theme template files, use:
 *    <div id="et_mobile_nav_menu">
 *        <span class="select_page"><?php esc_html_e('Select Page', 'Divi'); ?></span>
 *        <button class="mobile-menu__button">
 *            <i class="fas fa-bars"></i>
 *        </button>
 *        <?php display_mobile_menu(); ?>
 *    </div>
 *
 * 2. Or use the WordPress nav menu function directly:
 *    <?php
 *        wp_nav_menu(array(
 *            'theme_location' => 'mobile-menu',
 *            'walker' => new Mobile_Menu_Walker(),
 *            'container' => false,
 *            'menu_class' => 'mobile-menu',
 *            'fallback_cb' => false
 *        ));
 *    ?>
 *
 * 3. As shortcode in content:
 *    [mobile_menu]
 *
 * 4. To add icons to menu items:
 *    - Go to Appearance > Menus in WordPress admin
 *    - Edit a menu item
 *    - In the "Icon Class" field, enter Font Awesome classes like:
 *      * fas fa-laptop (for Products)
 *      * fas fa-cogs (for Services)
 *      * fas fa-book (for Resources)
 *      * fas fa-handshake (for Partners)
 *      * fas fa-chart-bar (for Overview)
 *      * fas fa-calendar (for Events)
 *      * fas fa-envelope (for Contact)
 *      * fas fa-globe (for Regions)
 *
 * 5. Menu structure requirements:
 *    - Top level items become Level 1 menu items
 *    - Second level items become Level 2 submenus
 *    - Third level items become Level 3 sub-submenus
 *    - The walker automatically generates proper BEM classes and structure
 *
 * 6. The generated HTML structure matches exactly:
 *    <div class="mobile-menu">
 *        <div class="mobile-menu__header">...</div>
 *        <div class="mobile-menu__container">
 *            <div class="mobile-menu__level mobile-menu__level--level-1">...</div>
 *            <div class="mobile-menu__level mobile-menu__level--level-2">...</div>
 *            <div class="mobile-menu__level mobile-menu__level--level-3">...</div>
 *        </div>
 *        <a class="mobile-menu__footer-link">...</a>
 *    </div>
 */