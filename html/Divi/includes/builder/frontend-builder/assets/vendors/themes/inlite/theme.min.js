!function(_){"use strict";var u,t,e,n,i,r=tinymce.util.Tools.resolve("tinymce.ThemeManager"),h=tinymce.util.Tools.resolve("tinymce.Env"),v=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),c=tinymce.util.Tools.resolve("tinymce.util.Delay"),o=function(t){return t.reduce(function(t,e){return Array.isArray(e)?t.concat(o(e)):t.concat(e)},[])},s={flatten:o},a=function(t,e){for(var n=0;n<e.length;n++){var i=(0,e[n])(t);if(i)return i}return null},l=function(t,e){return{id:t,rect:e}},d=function(t){return{x:t.left,y:t.top,w:t.width,h:t.height}},f=function(t){return{left:t.x,top:t.y,width:t.w,height:t.h,right:t.x+t.w,bottom:t.y+t.h}},m=function(t){var e=v.DOM.getViewPort();return{x:t.x+e.x,y:t.y+e.y,w:t.w,h:t.h}},g=function(t){var e=t.getBoundingClientRect();return m({x:e.left,y:e.top,w:Math.max(t.clientWidth,t.offsetWidth),h:Math.max(t.clientHeight,t.offsetHeight)})},p=function(t,e){return g(e)},b=function(t){return g(t.getContentAreaContainer()||t.getBody())},y=function(t){var e=t.selection.getBoundingClientRect();return e?m(d(e)):null},x=function(n,i){return function(t){for(var e=0;e<i.length;e++)if(i[e].predicate(n))return l(i[e].id,p(t,n));return null}},w=function(i,r){return function(t){for(var e=0;e<i.length;e++)for(var n=0;n<r.length;n++)if(r[n].predicate(i[e]))return l(r[n].id,p(t,i[e]));return null}},R=tinymce.util.Tools.resolve("tinymce.util.Tools"),C=function(t,e){return{id:t,predicate:e}},k=function(t){return R.map(t,function(t){return C(t.id,t.predicate)})},E=function(e){return function(t){return t.selection.isCollapsed()?null:l(e,y(t))}},H=function(i,r){return function(t){var e,n=t.schema.getTextBlockElements();for(e=0;e<i.length;e++)if("TABLE"===i[e].nodeName)return null;for(e=0;e<i.length;e++)if(i[e].nodeName in n)return t.dom.isEmpty(i[e])?l(r,y(t)):null;return null}},T=function(t){t.fire("SkinLoaded")},S=function(t){return t.fire("BeforeRenderUI")},M=tinymce.util.Tools.resolve("tinymce.EditorManager"),N=function(e){return function(t){return typeof t===e}},O=function(t){return Array.isArray(t)},W=function(t){return N("string")(t)},P=function(t){return N("number")(t)},D=function(t){return N("boolean")(t)},A=function(t){return N("function")(t)},B=(N("object"),O),L=function(t,e){if(e(t))return!0;throw new Error("Default value doesn't match requested type.")},I=function(r){return function(t,e,n){var i=t.settings;return L(n,r),e in i&&r(i[e])?i[e]:n}},z={getStringOr:I(W),getBoolOr:I(D),getNumberOr:I(P),getHandlerOr:I(A),getToolbarItemsOr:(u=B,function(t,e,n){var i,r,o,s,a,l=e in t.settings?t.settings[e]:n;return L(n,u),r=n,B(i=l)?i:W(i)?"string"==typeof(s=i)?(a=/[ ,]/,s.split(a).filter(function(t){return 0<t.length})):s:D(i)?(o=r,!1===i?[]:o):r})},F=tinymce.util.Tools.resolve("tinymce.geom.Rect"),U=function(t,e){return{rect:t,position:e}},V=function(t,e){return{x:e.x,y:e.y,w:t.w,h:t.h}},q=function(t,e,n,i,r){var o,s,a,l={x:i.x,y:i.y,w:i.w+(i.w<r.w+n.w?r.w:0),h:i.h+(i.h<r.h+n.h?r.h:0)};return o=F.findBestRelativePosition(r,n,l,t),n=F.clamp(n,l),o?(s=F.relativePosition(r,n,o),a=V(r,s),U(a,o)):(n=F.intersect(l,n))?((o=F.findBestRelativePosition(r,n,l,e))?(s=F.relativePosition(r,n,o),a=V(r,s)):a=V(r,n),U(a,o)):null},Y=function(t,e,n){return q(["cr-cl","cl-cr"],["bc-tc","bl-tl","br-tr"],t,e,n)},$=function(t,e,n){return q(["tc-bc","bc-tc","tl-bl","bl-tl","tr-br","br-tr","cr-cl","cl-cr"],["bc-tc","bl-tl","br-tr","cr-cl"],t,e,n)},X=function(t,e,n,i){var r;return"function"==typeof t?(r=t({elementRect:f(e),contentAreaRect:f(n),panelRect:f(i)}),d(r)):i},j=function(t){return t.panelRect},J=function(t){return z.getToolbarItemsOr(t,"selection_toolbar",["bold","italic","|","quicklink","h2","h3","blockquote"])},G=function(t){return z.getToolbarItemsOr(t,"insert_toolbar",["quickimage","quicktable"])},K=function(t){return z.getHandlerOr(t,"inline_toolbar_position_handler",j)},Z=function(t){var e,n,i,r,o=t.settings;return o.skin_url?(i=t,r=o.skin_url,i.documentBaseURI.toAbsolute(r)):(e=o.skin,n=M.baseURL+"/skins/",e?n+e:n+"lightgray")},Q=function(t){return!1===t.settings.skin},tt=function(i,r){var t=Z(i),e=function(){var t,e,n;e=r,n=function(){t._skinLoaded=!0,T(t),e()},(t=i).initialized?n():t.on("init",n)};Q(i)?e():(v.DOM.styleSheetLoader.load(t+"/skin.min.css",e),i.contentCSS.push(t+"/content.inline.min.css"))},et=function(t){var e,n,i,r,o=t.contextToolbars;return s.flatten([o||[],(e=t,n="img",i="image",r="alignleft aligncenter alignright",{predicate:function(t){return e.dom.is(t,n)},id:i,items:r})])},nt=function(t,e){var n,i,r,o,s;return s=(o=t).selection.getNode(),i=o.dom.getParents(s,"*"),r=k(e),(n=a(t,[x(i[0],r),E("text"),H(i,"insert"),w(i,r)]))&&n.rect?n:null},it=function(i,r){return function(){var t,e,n;i.removed||(n=i,_.document.activeElement!==n.getBody())||(t=et(i),(e=nt(i,t))?r.show(i,e.id,e.rect,t):r.hide())}},rt=function(t,e){var n,i,r,o,s,a=c.throttle(it(t,e),0),l=c.throttle((r=it(n=t,i=e),function(){n.removed||i.inForm()||r()}),0),u=(o=t,s=e,function(){var t=et(o),e=nt(o,t);e&&s.reposition(o,e.id,e.rect)});t.on("blur hide ObjectResizeStart",e.hide),t.on("click",a),t.on("nodeChange mouseup",l),t.on("ResizeEditor keyup",a),t.on("ResizeWindow",u),v.DOM.bind(h.container,"scroll",u),t.on("remove",function(){v.DOM.unbind(h.container,"scroll",u),e.remove()}),t.shortcuts.add("Alt+F10,F10","",e.focus)},ot=function(t,e){return tt(t,function(){var n,i;rt(t,e),i=e,(n=t).shortcuts.remove("meta+k"),n.shortcuts.add("meta+k","",function(){var t=et(n),e=a(n,[E("quicklink")]);e&&i.show(n,e.id,e.rect,t)})}),{}},st=function(t,e){return t.inline?ot(t,e):function(t){throw new Error(t)}("inlite theme only supports inline mode.")},at=function(){},lt=function(t){return function(){return t}},ut=lt(!1),ct=lt(!0),dt=function(){return ft},ft=(t=function(t){return t.isNone()},i={fold:function(t,e){return t()},is:ut,isSome:ut,isNone:ct,getOr:n=function(t){return t},getOrThunk:e=function(t){return t()},getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:lt(null),getOrUndefined:lt(undefined),or:n,orThunk:e,map:dt,each:at,bind:dt,exists:ut,forall:ct,filter:dt,equals:t,equals_:t,toArray:function(){return[]},toString:lt("none()")},Object.freeze&&Object.freeze(i),i),ht=function(n){var t=lt(n),e=function(){return r},i=function(t){return t(n)},r={fold:function(t,e){return e(n)},is:function(t){return n===t},isSome:ct,isNone:ut,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:e,orThunk:e,map:function(t){return ht(t(n))},each:function(t){t(n)},bind:i,exists:i,forall:i,filter:function(t){return t(n)?r:ft},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(t){return t.is(n)},equals_:function(t,e){return t.fold(ut,function(t){return e(n,t)})}};return r},mt={some:ht,none:dt,from:function(t){return null===t||t===undefined?ft:ht(t)}},gt=function(e){return function(t){return function(t){if(null===t)return"null";var e=typeof t;return"object"===e&&(Array.prototype.isPrototypeOf(t)||t.constructor&&"Array"===t.constructor.name)?"array":"object"===e&&(String.prototype.isPrototypeOf(t)||t.constructor&&"String"===t.constructor.name)?"string":e}(t)===e}},pt=gt("array"),vt=gt("function"),bt=gt("number"),yt=(Array.prototype.slice,Array.prototype.indexOf),xt=Array.prototype.push,wt=function(t,e){var n,i,r=(n=t,i=e,yt.call(n,i));return-1===r?mt.none():mt.some(r)},_t=function(t,e){for(var n=0,i=t.length;n<i;n++)if(e(t[n],n))return!0;return!1},Rt=function(t,e){for(var n=t.length,i=new Array(n),r=0;r<n;r++){var o=t[r];i[r]=e(o,r)}return i},Ct=function(t,e){for(var n=0,i=t.length;n<i;n++)e(t[n],n)},kt=function(t,e){for(var n=[],i=0,r=t.length;i<r;i++){var o=t[i];e(o,i)&&n.push(o)}return n},Et=(vt(Array.from)&&Array.from,0),Ht={id:function(){return"mceu_"+Et++},create:function(t,e,n){var i=_.document.createElement(t);return v.DOM.setAttribs(i,e),"string"==typeof n?i.innerHTML=n:R.each(n,function(t){t.nodeType&&i.appendChild(t)}),i},createFragment:function(t){return v.DOM.createFragment(t)},getWindowSize:function(){return v.DOM.getViewPort()},getSize:function(t){var e,n;if(t.getBoundingClientRect){var i=t.getBoundingClientRect();e=Math.max(i.width||i.right-i.left,t.offsetWidth),n=Math.max(i.height||i.bottom-i.bottom,t.offsetHeight)}else e=t.offsetWidth,n=t.offsetHeight;return{width:e,height:n}},getPos:function(t,e){return v.DOM.getPos(t,e||Ht.getContainer())},getContainer:function(){return h.container?h.container:_.document.body},getViewPort:function(t){return v.DOM.getViewPort(t)},get:function(t){return _.document.getElementById(t)},addClass:function(t,e){return v.DOM.addClass(t,e)},removeClass:function(t,e){return v.DOM.removeClass(t,e)},hasClass:function(t,e){return v.DOM.hasClass(t,e)},toggleClass:function(t,e,n){return v.DOM.toggleClass(t,e,n)},css:function(t,e,n){return v.DOM.setStyle(t,e,n)},getRuntimeStyle:function(t,e){return v.DOM.getStyle(t,e,!0)},on:function(t,e,n,i){return v.DOM.bind(t,e,n,i)},off:function(t,e,n){return v.DOM.unbind(t,e,n)},fire:function(t,e,n){return v.DOM.fire(t,e,n)},innerHtml:function(t,e){v.DOM.setHTML(t,e)}},Tt=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),St=tinymce.util.Tools.resolve("tinymce.util.Class"),Mt=tinymce.util.Tools.resolve("tinymce.util.EventDispatcher"),Nt=function(t){var e;if(t)return"number"==typeof t?{top:t=t||0,left:t,bottom:t,right:t}:(1===(e=(t=t.split(" ")).length)?t[1]=t[2]=t[3]=t[0]:2===e?(t[2]=t[0],t[3]=t[1]):3===e&&(t[3]=t[1]),{top:parseInt(t[0],10)||0,right:parseInt(t[1],10)||0,bottom:parseInt(t[2],10)||0,left:parseInt(t[3],10)||0})},Ot=function(i,t){function e(t){var e=parseFloat(function(t){var e=i.ownerDocument.defaultView;if(e){var n=e.getComputedStyle(i,null);return n?(t=t.replace(/[A-Z]/g,function(t){return"-"+t}),n.getPropertyValue(t)):null}return i.currentStyle[t]}(t));return isNaN(e)?0:e}return{top:e(t+"TopWidth"),right:e(t+"RightWidth"),bottom:e(t+"BottomWidth"),left:e(t+"LeftWidth")}};function Wt(){}function Pt(t){this.cls=[],this.cls._map={},this.onchange=t||Wt,this.prefix=""}R.extend(Pt.prototype,{add:function(t){return t&&!this.contains(t)&&(this.cls._map[t]=!0,this.cls.push(t),this._change()),this},remove:function(t){if(this.contains(t)){var e=void 0;for(e=0;e<this.cls.length&&this.cls[e]!==t;e++);this.cls.splice(e,1),delete this.cls._map[t],this._change()}return this},toggle:function(t,e){var n=this.contains(t);return n!==e&&(n?this.remove(t):this.add(t),this._change()),this},contains:function(t){return!!this.cls._map[t]},_change:function(){delete this.clsValue,this.onchange.call(this)}}),Pt.prototype.toString=function(){var t;if(this.clsValue)return this.clsValue;t="";for(var e=0;e<this.cls.length;e++)0<e&&(t+=" "),t+=this.prefix+this.cls[e];return t};var Dt,At,Bt,Lt=/^([\w\\*]+)?(?:#([\w\-\\]+))?(?:\.([\w\\\.]+))?(?:\[\@?([\w\\]+)([\^\$\*!~]?=)([\w\\]+)\])?(?:\:(.+))?/i,It=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g,zt=/^\s*|\s*$/g,Ft=St.extend({init:function(t){var o=this.match;function s(t,e,n){var i;function r(t){t&&e.push(t)}return r(function(e){if(e)return e=e.toLowerCase(),function(t){return"*"===e||t.type===e}}((i=Lt.exec(t.replace(zt,"")))[1])),r(function(e){if(e)return function(t){return t._name===e}}(i[2])),r(function(n){if(n)return n=n.split("."),function(t){for(var e=n.length;e--;)if(!t.classes.contains(n[e]))return!1;return!0}}(i[3])),r(function(n,i,r){if(n)return function(t){var e=t[n]?t[n]():"";return i?"="===i?e===r:"*="===i?0<=e.indexOf(r):"~="===i?0<=(" "+e+" ").indexOf(" "+r+" "):"!="===i?e!==r:"^="===i?0===e.indexOf(r):"$="===i&&e.substr(e.length-r.length)===r:!!r}}(i[4],i[5],i[6])),r(function(i){var e;if(i)return(i=/(?:not\((.+)\))|(.+)/i.exec(i))[1]?(e=a(i[1],[]),function(t){return!o(t,e)}):(i=i[2],function(t,e,n){return"first"===i?0===e:"last"===i?e===n-1:"even"===i?e%2==0:"odd"===i?e%2==1:!!t[i]&&t[i]()})}(i[7])),e.pseudo=!!i[7],e.direct=n,e}function a(t,e){var n,i,r,o=[];do{if(It.exec(""),(i=It.exec(t))&&(t=i[3],o.push(i[1]),i[2])){n=i[3];break}}while(i);for(n&&a(n,e),t=[],r=0;r<o.length;r++)">"!==o[r]&&t.push(s(o[r],[],">"===o[r-1]));return e.push(t),e}this._selectors=a(t,[])},match:function(t,e){var n,i,r,o,s,a,l,u,c,d,f,h,m;for(n=0,i=(e=e||this._selectors).length;n<i;n++){for(m=t,h=0,r=(o=(s=e[n]).length)-1;0<=r;r--)for(u=s[r];m;){if(u.pseudo)for(c=d=(f=m.parent().items()).length;c--&&f[c]!==m;);for(a=0,l=u.length;a<l;a++)if(!u[a](m,c,d)){a=l+1;break}if(a===l){h++;break}if(r===o-1)break;m=m.parent()}if(h===o)return!0}return!1},find:function(t){var e,n,u=[],i=this._selectors;function c(t,e,n){var i,r,o,s,a,l=e[n];for(i=0,r=t.length;i<r;i++){for(a=t[i],o=0,s=l.length;o<s;o++)if(!l[o](a,i,r)){o=s+1;break}if(o===s)n===e.length-1?u.push(a):a.items&&c(a.items(),e,n+1);else if(l.direct)return;a.items&&c(a.items(),e,n)}}if(t.items){for(e=0,n=i.length;e<n;e++)c(t.items(),i[e],0);1<n&&(u=function(t){for(var e,n=[],i=t.length;i--;)(e=t[i]).__checked||(n.push(e),e.__checked=1);for(i=n.length;i--;)delete n[i].__checked;return n}(u))}return Dt||(Dt=Ft.Collection),new Dt(u)}}),Ut=Array.prototype.push,Vt=Array.prototype.slice;Bt={length:0,init:function(t){t&&this.add(t)},add:function(t){return R.isArray(t)?Ut.apply(this,t):t instanceof At?this.add(t.toArray()):Ut.call(this,t),this},set:function(t){var e,n=this,i=n.length;for(n.length=0,n.add(t),e=n.length;e<i;e++)delete n[e];return n},filter:function(e){var t,n,i,r,o=[];for("string"==typeof e?(e=new Ft(e),r=function(t){return e.match(t)}):r=e,t=0,n=this.length;t<n;t++)r(i=this[t])&&o.push(i);return new At(o)},slice:function(){return new At(Vt.apply(this,arguments))},eq:function(t){return-1===t?this.slice(t):this.slice(t,+t+1)},each:function(t){return R.each(this,t),this},toArray:function(){return R.toArray(this)},indexOf:function(t){for(var e=this.length;e--&&this[e]!==t;);return e},reverse:function(){return new At(R.toArray(this).reverse())},hasClass:function(t){return!!this[0]&&this[0].classes.contains(t)},prop:function(e,n){var t;return n!==undefined?(this.each(function(t){t[e]&&t[e](n)}),this):(t=this[0])&&t[e]?t[e]():void 0},exec:function(e){var n=R.toArray(arguments).slice(1);return this.each(function(t){t[e]&&t[e].apply(t,n)}),this},remove:function(){for(var t=this.length;t--;)this[t].remove();return this},addClass:function(e){return this.each(function(t){t.classes.add(e)})},removeClass:function(e){return this.each(function(t){t.classes.remove(e)})}},R.each("fire on off show hide append prepend before after reflow".split(" "),function(n){Bt[n]=function(){var e=R.toArray(arguments);return this.each(function(t){n in t&&t[n].apply(t,e)}),this}}),R.each("text name disabled active selected checked visible parent value data".split(" "),function(e){Bt[e]=function(t){return this.prop(e,t)}}),At=St.extend(Bt);var qt=Ft.Collection=At,Yt=function(t){this.create=t.create};Yt.create=function(r,o){return new Yt({create:function(e,n){var i,t=function(t){e.set(n,t.value)};return e.on("change:"+n,function(t){r.set(o,t.value)}),r.on("change:"+o,t),(i=e._bindings)||(i=e._bindings=[],e.on("destroy",function(){for(var t=i.length;t--;)i[t]()})),i.push(function(){r.off("change:"+o,t)}),r.get(o)}})};var $t=tinymce.util.Tools.resolve("tinymce.util.Observable");function Xt(t){return 0<t.nodeType}var jt,Jt,Gt=St.extend({Mixins:[$t],init:function(t){var e,n;for(e in t=t||{})(n=t[e])instanceof Yt&&(t[e]=n.create(this,e));this.data=t},set:function(e,n){var i,r,o=this.data[e];if(n instanceof Yt&&(n=n.create(this,e)),"object"==typeof e){for(i in e)this.set(i,e[i]);return this}return function t(e,n){var i,r;if(e===n)return!0;if(null===e||null===n)return e===n;if("object"!=typeof e||"object"!=typeof n)return e===n;if(R.isArray(n)){if(e.length!==n.length)return!1;for(i=e.length;i--;)if(!t(e[i],n[i]))return!1}if(Xt(e)||Xt(n))return e===n;for(i in r={},n){if(!t(e[i],n[i]))return!1;r[i]=!0}for(i in e)if(!r[i]&&!t(e[i],n[i]))return!1;return!0}(o,n)||(this.data[e]=n,r={target:this,name:e,value:n,oldValue:o},this.fire("change:"+e,r),this.fire("change",r)),this},get:function(t){return this.data[t]},has:function(t){return t in this.data},bind:function(t){return Yt.create(this,t)},destroy:function(){this.fire("destroy")}}),Kt={},Zt={add:function(t){var e=t.parent();if(e){if(!e._layout||e._layout.isNative())return;Kt[e._id]||(Kt[e._id]=e),jt||(jt=!0,c.requestAnimationFrame(function(){var t,e;for(t in jt=!1,Kt)(e=Kt[t]).state.get("rendered")&&e.reflow();Kt={}},_.document.body))}},remove:function(t){Kt[t._id]&&delete Kt[t._id]}},Qt=function(t){return t?t.getRoot().uiContainer:null},te={getUiContainerDelta:function(t){var e=Qt(t);if(e&&"static"!==v.DOM.getStyle(e,"position",!0)){var n=v.DOM.getPos(e),i=e.scrollLeft-n.x,r=e.scrollTop-n.y;return mt.some({x:i,y:r})}return mt.none()},setUiContainer:function(t,e){var n=v.DOM.select(t.settings.ui_container)[0];e.getRoot().uiContainer=n},getUiContainer:Qt,inheritUiContainer:function(t,e){return e.uiContainer=Qt(t)}},ee="onmousewheel"in _.document,ne=!1,ie=0,re={Statics:{classPrefix:"mce-"},isRtl:function(){return Jt.rtl},classPrefix:"mce-",init:function(e){var t,n,i=this;function r(t){var e;for(t=t.split(" "),e=0;e<t.length;e++)i.classes.add(t[e])}i.settings=e=R.extend({},i.Defaults,e),i._id=e.id||"mceu_"+ie++,i._aria={role:e.role},i._elmCache={},i.$=Tt,i.state=new Gt({visible:!0,active:!1,disabled:!1,value:""}),i.data=new Gt(e.data),i.classes=new Pt(function(){i.state.get("rendered")&&(i.getEl().className=this.toString())}),i.classes.prefix=i.classPrefix,(t=e.classes)&&(i.Defaults&&(n=i.Defaults.classes)&&t!==n&&r(n),r(t)),R.each("title text name visible disabled active value".split(" "),function(t){t in e&&i[t](e[t])}),i.on("click",function(){if(i.disabled())return!1}),i.settings=e,i.borderBox=Nt(e.border),i.paddingBox=Nt(e.padding),i.marginBox=Nt(e.margin),e.hidden&&i.hide()},Properties:"parent,name",getContainerElm:function(){var t=te.getUiContainer(this);return t||Ht.getContainer()},getParentCtrl:function(t){for(var e,n=this.getRoot().controlIdLookup;t&&n&&!(e=n[t.id]);)t=t.parentNode;return e},initLayoutRect:function(){var t,e,n,i,r,o,s,a,l,u,c=this,d=c.settings,f=c.getEl();t=c.borderBox=c.borderBox||Ot(f,"border"),c.paddingBox=c.paddingBox||Ot(f,"padding"),c.marginBox=c.marginBox||Ot(f,"margin"),u=Ht.getSize(f),a=d.minWidth,l=d.minHeight,r=a||u.width,o=l||u.height,n=d.width,i=d.height,s=void 0!==(s=d.autoResize)?s:!n&&!i,n=n||r,i=i||o;var h=t.left+t.right,m=t.top+t.bottom,g=d.maxWidth||65535,p=d.maxHeight||65535;return c._layoutRect=e={x:d.x||0,y:d.y||0,w:n,h:i,deltaW:h,deltaH:m,contentW:n-h,contentH:i-m,innerW:n-h,innerH:i-m,startMinWidth:a||0,startMinHeight:l||0,minW:Math.min(r,g),minH:Math.min(o,p),maxW:g,maxH:p,autoResize:s,scrollW:0},c._lastLayoutRect={},e},layoutRect:function(t){var e,n,i,r,o,s=this,a=s._layoutRect;return a||(a=s.initLayoutRect()),t?(i=a.deltaW,r=a.deltaH,t.x!==undefined&&(a.x=t.x),t.y!==undefined&&(a.y=t.y),t.minW!==undefined&&(a.minW=t.minW),t.minH!==undefined&&(a.minH=t.minH),(n=t.w)!==undefined&&(n=(n=n<a.minW?a.minW:n)>a.maxW?a.maxW:n,a.w=n,a.innerW=n-i),(n=t.h)!==undefined&&(n=(n=n<a.minH?a.minH:n)>a.maxH?a.maxH:n,a.h=n,a.innerH=n-r),(n=t.innerW)!==undefined&&(n=(n=n<a.minW-i?a.minW-i:n)>a.maxW-i?a.maxW-i:n,a.innerW=n,a.w=n+i),(n=t.innerH)!==undefined&&(n=(n=n<a.minH-r?a.minH-r:n)>a.maxH-r?a.maxH-r:n,a.innerH=n,a.h=n+r),t.contentW!==undefined&&(a.contentW=t.contentW),t.contentH!==undefined&&(a.contentH=t.contentH),(e=s._lastLayoutRect).x===a.x&&e.y===a.y&&e.w===a.w&&e.h===a.h||((o=Jt.repaintControls)&&o.map&&!o.map[s._id]&&(o.push(s),o.map[s._id]=!0),e.x=a.x,e.y=a.y,e.w=a.w,e.h=a.h),s):a},repaint:function(){var t,e,n,i,r,o,s,a,l,u,c=this;l=_.document.createRange?function(t){return t}:Math.round,t=c.getEl().style,i=c._layoutRect,a=c._lastRepaintRect||{},o=(r=c.borderBox).left+r.right,s=r.top+r.bottom,i.x!==a.x&&(t.left=l(i.x)+"px",a.x=i.x),i.y!==a.y&&(t.top=l(i.y)+"px",a.y=i.y),i.w!==a.w&&(u=l(i.w-o),t.width=(0<=u?u:0)+"px",a.w=i.w),i.h!==a.h&&(u=l(i.h-s),t.height=(0<=u?u:0)+"px",a.h=i.h),c._hasBody&&i.innerW!==a.innerW&&(u=l(i.innerW),(n=c.getEl("body"))&&((e=n.style).width=(0<=u?u:0)+"px"),a.innerW=i.innerW),c._hasBody&&i.innerH!==a.innerH&&(u=l(i.innerH),(n=n||c.getEl("body"))&&((e=e||n.style).height=(0<=u?u:0)+"px"),a.innerH=i.innerH),c._lastRepaintRect=a,c.fire("repaint",{},!1)},updateLayoutRect:function(){var t=this;t.parent()._lastRect=null,Ht.css(t.getEl(),{width:"",height:""}),t._layoutRect=t._lastRepaintRect=t._lastLayoutRect=null,t.initLayoutRect()},on:function(t,e){var n,i,r,o=this;return oe(o).on(t,"string"!=typeof(n=e)?n:function(t){return i||o.parentsAndSelf().each(function(t){var e=t.settings.callbacks;if(e&&(i=e[n]))return r=t,!1}),i?i.call(r,t):(t.action=n,void this.fire("execute",t))}),o},off:function(t,e){return oe(this).off(t,e),this},fire:function(t,e,n){if((e=e||{}).control||(e.control=this),e=oe(this).fire(t,e),!1!==n&&this.parent)for(var i=this.parent();i&&!e.isPropagationStopped();)i.fire(t,e,!1),i=i.parent();return e},hasEventListeners:function(t){return oe(this).has(t)},parents:function(t){var e,n=new qt;for(e=this.parent();e;e=e.parent())n.add(e);return t&&(n=n.filter(t)),n},parentsAndSelf:function(t){return new qt(this).add(this.parents(t))},next:function(){var t=this.parent().items();return t[t.indexOf(this)+1]},prev:function(){var t=this.parent().items();return t[t.indexOf(this)-1]},innerHtml:function(t){return this.$el.html(t),this},getEl:function(t){var e=t?this._id+"-"+t:this._id;return this._elmCache[e]||(this._elmCache[e]=Tt("#"+e)[0]),this._elmCache[e]},show:function(){return this.visible(!0)},hide:function(){return this.visible(!1)},focus:function(){try{this.getEl().focus()}catch(t){}return this},blur:function(){return this.getEl().blur(),this},aria:function(t,e){var n=this,i=n.getEl(n.ariaTarget);return void 0===e?n._aria[t]:(n._aria[t]=e,n.state.get("rendered")&&i.setAttribute("role"===t?t:"aria-"+t,e),n)},encode:function(t,e){return!1!==e&&(t=this.translate(t)),(t||"").replace(/[&<>"]/g,function(t){return"&#"+t.charCodeAt(0)+";"})},translate:function(t){return Jt.translate?Jt.translate(t):t},before:function(t){var e=this.parent();return e&&e.insert(t,e.items().indexOf(this),!0),this},after:function(t){var e=this.parent();return e&&e.insert(t,e.items().indexOf(this)),this},remove:function(){var e,t,n=this,i=n.getEl(),r=n.parent();if(n.items){var o=n.items().toArray();for(t=o.length;t--;)o[t].remove()}r&&r.items&&(e=[],r.items().each(function(t){t!==n&&e.push(t)}),r.items().set(e),r._lastRect=null),n._eventsRoot&&n._eventsRoot===n&&Tt(i).off();var s=n.getRoot().controlIdLookup;return s&&delete s[n._id],i&&i.parentNode&&i.parentNode.removeChild(i),n.state.set("rendered",!1),n.state.destroy(),n.fire("remove"),n},renderBefore:function(t){return Tt(t).before(this.renderHtml()),this.postRender(),this},renderTo:function(t){return Tt(t||this.getContainerElm()).append(this.renderHtml()),this.postRender(),this},preRender:function(){},render:function(){},renderHtml:function(){return'<div id="'+this._id+'" class="'+this.classes+'"></div>'},postRender:function(){var t,e,n,i,r,o=this,s=o.settings;for(i in o.$el=Tt(o.getEl()),o.state.set("rendered",!0),s)0===i.indexOf("on")&&o.on(i.substr(2),s[i]);if(o._eventsRoot){for(n=o.parent();!r&&n;n=n.parent())r=n._eventsRoot;if(r)for(i in r._nativeEvents)o._nativeEvents[i]=!0}se(o),s.style&&(t=o.getEl())&&(t.setAttribute("style",s.style),t.style.cssText=s.style),o.settings.border&&(e=o.borderBox,o.$el.css({"border-top-width":e.top,"border-right-width":e.right,"border-bottom-width":e.bottom,"border-left-width":e.left}));var a=o.getRoot();for(var l in a.controlIdLookup||(a.controlIdLookup={}),(a.controlIdLookup[o._id]=o)._aria)o.aria(l,o._aria[l]);!1===o.state.get("visible")&&(o.getEl().style.display="none"),o.bindStates(),o.state.on("change:visible",function(t){var e,n=t.value;o.state.get("rendered")&&(o.getEl().style.display=!1===n?"none":"",o.getEl().getBoundingClientRect()),(e=o.parent())&&(e._lastRect=null),o.fire(n?"show":"hide"),Zt.add(o)}),o.fire("postrender",{},!1)},bindStates:function(){},scrollIntoView:function(t){var e,n,i,r,o,s,a=this.getEl(),l=a.parentNode,u=function(t,e){var n,i,r=t;for(n=i=0;r&&r!==e&&r.nodeType;)n+=r.offsetLeft||0,i+=r.offsetTop||0,r=r.offsetParent;return{x:n,y:i}}(a,l);return e=u.x,n=u.y,i=a.offsetWidth,r=a.offsetHeight,o=l.clientWidth,s=l.clientHeight,"end"===t?(e-=o-i,n-=s-r):"center"===t&&(e-=o/2-i/2,n-=s/2-r/2),l.scrollLeft=e,l.scrollTop=n,this},getRoot:function(){for(var t,e=this,n=[];e;){if(e.rootControl){t=e.rootControl;break}n.push(e),e=(t=e).parent()}t||(t=this);for(var i=n.length;i--;)n[i].rootControl=t;return t},reflow:function(){Zt.remove(this);var t=this.parent();return t&&t._layout&&!t._layout.isNative()&&t.reflow(),this}};function oe(n){return n._eventDispatcher||(n._eventDispatcher=new Mt({scope:n,toggleEvent:function(t,e){e&&Mt.isNative(t)&&(n._nativeEvents||(n._nativeEvents={}),n._nativeEvents[t]=!0,n.state.get("rendered")&&se(n))}})),n._eventDispatcher}function se(a){var t,e,n,l,i,r;function o(t){var e=a.getParentCtrl(t.target);e&&e.fire(t.type,t)}function s(){var t=l._lastHoverCtrl;t&&(t.fire("mouseleave",{target:t.getEl()}),t.parents().each(function(t){t.fire("mouseleave",{target:t.getEl()})}),l._lastHoverCtrl=null)}function u(t){var e,n,i,r=a.getParentCtrl(t.target),o=l._lastHoverCtrl,s=0;if(r!==o){if((n=(l._lastHoverCtrl=r).parents().toArray().reverse()).push(r),o){for((i=o.parents().toArray().reverse()).push(o),s=0;s<i.length&&n[s]===i[s];s++);for(e=i.length-1;s<=e;e--)(o=i[e]).fire("mouseleave",{target:o.getEl()})}for(e=s;e<n.length;e++)(r=n[e]).fire("mouseenter",{target:r.getEl()})}}function c(t){t.preventDefault(),"mousewheel"===t.type?(t.deltaY=-.025*t.wheelDelta,t.wheelDeltaX&&(t.deltaX=-.025*t.wheelDeltaX)):(t.deltaX=0,t.deltaY=t.detail),t=a.fire("wheel",t)}if(i=a._nativeEvents){for((n=a.parents().toArray()).unshift(a),t=0,e=n.length;!l&&t<e;t++)l=n[t]._eventsRoot;for(l||(l=n[n.length-1]||a),a._eventsRoot=l,e=t,t=0;t<e;t++)n[t]._eventsRoot=l;var d=l._delegates;for(r in d||(d=l._delegates={}),i){if(!i)return!1;"wheel"!==r||ne?("mouseenter"===r||"mouseleave"===r?l._hasMouseEnter||(Tt(l.getEl()).on("mouseleave",s).on("mouseover",u),l._hasMouseEnter=1):d[r]||(Tt(l.getEl()).on(r,o),d[r]=!0),i[r]=!1):ee?Tt(a.getEl()).on("mousewheel",c):Tt(a.getEl()).on("DOMMouseScroll",c)}}}R.each("text title visible disabled active value".split(" "),function(e){re[e]=function(t){return 0===arguments.length?this.state.get(e):(void 0!==t&&this.state.set(e,t),this)}});var ae=Jt=St.extend(re),le=function(t){return"static"===Ht.getRuntimeStyle(t,"position")},ue=function(t){return t.state.get("fixed")};function ce(t,e,n){var i,r,o,s,a,l,u,c,d,f;return d=de(),o=(r=Ht.getPos(e,te.getUiContainer(t))).x,s=r.y,ue(t)&&le(_.document.body)&&(o-=d.x,s-=d.y),i=t.getEl(),a=(f=Ht.getSize(i)).width,l=f.height,u=(f=Ht.getSize(e)).width,c=f.height,"b"===(n=(n||"").split(""))[0]&&(s+=c),"r"===n[1]&&(o+=u),"c"===n[0]&&(s+=Math.round(c/2)),"c"===n[1]&&(o+=Math.round(u/2)),"b"===n[3]&&(s-=l),"r"===n[4]&&(o-=a),"c"===n[3]&&(s-=Math.round(l/2)),"c"===n[4]&&(o-=Math.round(a/2)),{x:o,y:s,w:a,h:l}}var de=function(){var t=_.window;return{x:Math.max(t.pageXOffset,_.document.body.scrollLeft,_.document.documentElement.scrollLeft),y:Math.max(t.pageYOffset,_.document.body.scrollTop,_.document.documentElement.scrollTop),w:t.innerWidth||_.document.documentElement.clientWidth,h:t.innerHeight||_.document.documentElement.clientHeight}},fe=function(t){var e,n=te.getUiContainer(t);return n&&!ue(t)?{x:0,y:0,w:(e=n).scrollWidth-1,h:e.scrollHeight-1}:de()},he={testMoveRel:function(t,e){for(var n=fe(this),i=0;i<e.length;i++){var r=ce(this,t,e[i]);if(ue(this)){if(0<r.x&&r.x+r.w<n.w&&0<r.y&&r.y+r.h<n.h)return e[i]}else if(r.x>n.x&&r.x+r.w<n.w+n.x&&r.y>n.y&&r.y+r.h<n.h+n.y)return e[i]}return e[0]},moveRel:function(t,e){"string"!=typeof e&&(e=this.testMoveRel(t,e));var n=ce(this,t,e);return this.moveTo(n.x,n.y)},moveBy:function(t,e){var n=this.layoutRect();return this.moveTo(n.x+t,n.y+e),this},moveTo:function(t,e){var n=this;function i(t,e,n){return t<0?0:e<t+n&&(t=e-n)<0?0:t}if(n.settings.constrainToViewport){var r=fe(this),o=n.layoutRect();t=i(t,r.w+r.x,o.w),e=i(e,r.h+r.y,o.h)}var s=te.getUiContainer(n);return s&&le(s)&&!ue(n)&&(t-=s.scrollLeft,e-=s.scrollTop),s&&(t+=1,e+=1),n.state.get("rendered")?n.layoutRect({x:t,y:e}).repaint():(n.settings.x=t,n.settings.y=e),n.fire("move",{x:t,y:e}),n}},me=ae.extend({Mixins:[he],Defaults:{classes:"widget tooltip tooltip-n"},renderHtml:function(){var t=this,e=t.classPrefix;return'<div id="'+t._id+'" class="'+t.classes+'" role="presentation"><div class="'+e+'tooltip-arrow"></div><div class="'+e+'tooltip-inner">'+t.encode(t.state.get("text"))+"</div></div>"},bindStates:function(){var e=this;return e.state.on("change:text",function(t){e.getEl().lastChild.innerHTML=e.encode(t.value)}),e._super()},repaint:function(){var t,e;t=this.getEl().style,e=this._layoutRect,t.left=e.x+"px",t.top=e.y+"px",t.zIndex=131070}}),ge=ae.extend({init:function(i){var r=this;r._super(i),i=r.settings,r.canFocus=!0,i.tooltip&&!1!==ge.tooltips&&(r.on("mouseenter",function(t){var e=r.tooltip().moveTo(-65535);if(t.control===r){var n=e.text(i.tooltip).show().testMoveRel(r.getEl(),["bc-tc","bc-tl","bc-tr"]);e.classes.toggle("tooltip-n","bc-tc"===n),e.classes.toggle("tooltip-nw","bc-tl"===n),e.classes.toggle("tooltip-ne","bc-tr"===n),e.moveRel(r.getEl(),n)}else e.hide()}),r.on("mouseleave mousedown click",function(){r.tooltip().remove(),r._tooltip=null})),r.aria("label",i.ariaLabel||i.tooltip)},tooltip:function(){return this._tooltip||(this._tooltip=new me({type:"tooltip"}),te.inheritUiContainer(this,this._tooltip),this._tooltip.renderTo()),this._tooltip},postRender:function(){var t=this,e=t.settings;t._super(),t.parent()||!e.width&&!e.height||(t.initLayoutRect(),t.repaint()),e.autofocus&&t.focus()},bindStates:function(){var e=this;function n(t){e.aria("disabled",t),e.classes.toggle("disabled",t)}function i(t){e.aria("pressed",t),e.classes.toggle("active",t)}return e.state.on("change:disabled",function(t){n(t.value)}),e.state.on("change:active",function(t){i(t.value)}),e.state.get("disabled")&&n(!0),e.state.get("active")&&i(!0),e._super()},remove:function(){this._super(),this._tooltip&&(this._tooltip.remove(),this._tooltip=null)}}),pe=ge.extend({Defaults:{value:0},init:function(t){this._super(t),this.classes.add("progress"),this.settings.filter||(this.settings.filter=function(t){return Math.round(t)})},renderHtml:function(){var t=this._id,e=this.classPrefix;return'<div id="'+t+'" class="'+this.classes+'"><div class="'+e+'bar-container"><div class="'+e+'bar"></div></div><div class="'+e+'text">0%</div></div>'},postRender:function(){return this._super(),this.value(this.settings.value),this},bindStates:function(){var e=this;function n(t){t=e.settings.filter(t),e.getEl().lastChild.innerHTML=t+"%",e.getEl().firstChild.firstChild.style.width=t+"%"}return e.state.on("change:value",function(t){n(t.value)}),n(e.state.get("value")),e._super()}}),ve=function(t,e){t.getEl().lastChild.textContent=e+(t.progressBar?" "+t.progressBar.value()+"%":"")},be=ae.extend({Mixins:[he],Defaults:{classes:"widget notification"},init:function(t){var e=this;e._super(t),e.maxWidth=t.maxWidth,t.text&&e.text(t.text),t.icon&&(e.icon=t.icon),t.color&&(e.color=t.color),t.type&&e.classes.add("notification-"+t.type),t.timeout&&(t.timeout<0||0<t.timeout)&&!t.closeButton?e.closeButton=!1:(e.classes.add("has-close"),e.closeButton=!0),t.progressBar&&(e.progressBar=new pe),e.on("click",function(t){-1!==t.target.className.indexOf(e.classPrefix+"close")&&e.close()})},renderHtml:function(){var t,e=this,n=e.classPrefix,i="",r="",o="";return e.icon&&(i='<i class="'+n+"ico "+n+"i-"+e.icon+'"></i>'),t=' style="max-width: '+e.maxWidth+"px;"+(e.color?"background-color: "+e.color+';"':'"'),e.closeButton&&(r='<button type="button" class="'+n+'close" aria-hidden="true">\xd7</button>'),e.progressBar&&(o=e.progressBar.renderHtml()),'<div id="'+e._id+'" class="'+e.classes+'"'+t+' role="presentation">'+i+'<div class="'+n+'notification-inner">'+e.state.get("text")+"</div>"+o+r+'<div style="clip: rect(1px, 1px, 1px, 1px);height: 1px;overflow: hidden;position: absolute;width: 1px;" aria-live="assertive" aria-relevant="additions" aria-atomic="true"></div></div>'},postRender:function(){var t=this;return c.setTimeout(function(){t.$el.addClass(t.classPrefix+"in"),ve(t,t.state.get("text"))},100),t._super()},bindStates:function(){var e=this;return e.state.on("change:text",function(t){e.getEl().firstChild.innerHTML=t.value,ve(e,t.value)}),e.progressBar&&(e.progressBar.bindStates(),e.progressBar.state.on("change:value",function(t){ve(e,e.state.get("text"))})),e._super()},close:function(){return this.fire("close").isDefaultPrevented()||this.remove(),this},repaint:function(){var t,e;t=this.getEl().style,e=this._layoutRect,t.left=e.x+"px",t.top=e.y+"px",t.zIndex=65534}});function ye(o){var s=function(t){return t.inline?t.getElement():t.getContentAreaContainer()};return{open:function(t,e){var n,i=R.extend(t,{maxWidth:(n=s(o),Ht.getSize(n).width)}),r=new be(i);return 0<(r.args=i).timeout&&(r.timer=setTimeout(function(){r.close(),e()},i.timeout)),r.on("close",function(){e()}),r.renderTo(),r},close:function(t){t.close()},reposition:function(t){Ct(t,function(t){t.moveTo(0,0)}),function(n){if(0<n.length){var t=n.slice(0,1)[0],e=s(o);t.moveRel(e,"tc-tc"),Ct(n,function(t,e){0<e&&t.moveRel(n[e-1].getEl(),"bc-tc")})}}(t)},getArgs:function(t){return t.args}}}function xe(t){var e,n;if(t.changedTouches)for(e="screenX screenY pageX pageY clientX clientY".split(" "),n=0;n<e.length;n++)t[e[n]]=t.changedTouches[0][e[n]]}function we(t,h){var m,g,e,p,v,b,y,x=h.document||_.document;h=h||{};var w=x.getElementById(h.handle||t);e=function(t){var e,n,i,r,o,s,a,l,u,c,d,f=(e=x,u=Math.max,n=e.documentElement,i=e.body,r=u(n.scrollWidth,i.scrollWidth),o=u(n.clientWidth,i.clientWidth),s=u(n.offsetWidth,i.offsetWidth),a=u(n.scrollHeight,i.scrollHeight),l=u(n.clientHeight,i.clientHeight),{width:r<s?o:r,height:a<u(n.offsetHeight,i.offsetHeight)?l:a});xe(t),t.preventDefault(),g=t.button,c=w,b=t.screenX,y=t.screenY,d=_.window.getComputedStyle?_.window.getComputedStyle(c,null).getPropertyValue("cursor"):c.runtimeStyle.cursor,m=Tt("<div></div>").css({position:"absolute",top:0,left:0,width:f.width,height:f.height,zIndex:2147483647,opacity:1e-4,cursor:d}).appendTo(x.body),Tt(x).on("mousemove touchmove",v).on("mouseup touchend",p),h.start(t)},v=function(t){if(xe(t),t.button!==g)return p(t);t.deltaX=t.screenX-b,t.deltaY=t.screenY-y,t.preventDefault(),h.drag(t)},p=function(t){xe(t),Tt(x).off("mousemove touchmove",v).off("mouseup touchend",p),m.remove(),h.stop&&h.stop(t)},this.destroy=function(){Tt(w).off()},Tt(w).on("mousedown touchstart",e)}var _e=tinymce.util.Tools.resolve("tinymce.ui.Factory"),Re=function(t){return!!t.getAttribute("data-mce-tabstop")};function Ce(t){var o,r,n=t.root;function i(t){return t&&1===t.nodeType}try{o=_.document.activeElement}catch(e){o=_.document.body}function s(t){return i(t=t||o)?t.getAttribute("role"):null}function a(t){for(var e,n=t||o;n=n.parentNode;)if(e=s(n))return e}function l(t){var e=o;if(i(e))return e.getAttribute("aria-"+t)}function u(t){var e=t.tagName.toUpperCase();return"INPUT"===e||"TEXTAREA"===e||"SELECT"===e}function c(e){var r=[];return function t(e){if(1===e.nodeType&&"none"!==e.style.display&&!e.disabled){var n;(u(n=e)&&!n.hidden||Re(n)||/^(button|menuitem|checkbox|tab|menuitemcheckbox|option|gridcell|slider)$/.test(s(n)))&&r.push(e);for(var i=0;i<e.childNodes.length;i++)t(e.childNodes[i])}}(e||n.getEl()),r}function d(t){var e,n;(n=(t=t||r).parents().toArray()).unshift(t);for(var i=0;i<n.length&&!(e=n[i]).settings.ariaRoot;i++);return e}function f(t,e){return t<0?t=e.length-1:t>=e.length&&(t=0),e[t]&&e[t].focus(),t}function h(t,e){var n=-1,i=d();e=e||c(i.getEl());for(var r=0;r<e.length;r++)e[r]===o&&(n=r);n+=t,i.lastAriaIndex=f(n,e)}function m(){"tablist"===a()?h(-1,c(o.parentNode)):r.parent().submenu?b():h(-1)}function g(){var t=s(),e=a();"tablist"===e?h(1,c(o.parentNode)):"menuitem"===t&&"menu"===e&&l("haspopup")?y():h(1)}function p(){h(-1)}function v(){var t=s(),e=a();"menuitem"===t&&"menubar"===e?y():"button"===t&&l("haspopup")?y({key:"down"}):h(1)}function b(){r.fire("cancel")}function y(t){t=t||{},r.fire("click",{target:o,aria:t})}return r=n.getParentCtrl(o),n.on("keydown",function(t){function e(t,e){u(o)||Re(o)||"slider"!==s(o)&&!1!==e(t)&&t.preventDefault()}if(!t.isDefaultPrevented())switch(t.keyCode){case 37:e(t,m);break;case 39:e(t,g);break;case 38:e(t,p);break;case 40:e(t,v);break;case 27:b();break;case 14:case 13:case 32:e(t,y);break;case 9:!function(t){if("tablist"===a()){var e=c(r.getEl("body"))[0];e&&e.focus()}else h(t.shiftKey?-1:1)}(t),t.preventDefault()}}),n.on("focusin",function(t){o=t.target,r=t.control}),{focusFirst:function(t){var e=d(t),n=c(e.getEl());e.settings.ariaRemember&&"lastAriaIndex"in e?f(e.lastAriaIndex,n):f(0,n)}}}var ke,Ee,He,Te,Se={},Me=ae.extend({init:function(t){var e=this;e._super(t),(t=e.settings).fixed&&e.state.set("fixed",!0),e._items=new qt,e.isRtl()&&e.classes.add("rtl"),e.bodyClasses=new Pt(function(){e.state.get("rendered")&&(e.getEl("body").className=this.toString())}),e.bodyClasses.prefix=e.classPrefix,e.classes.add("container"),e.bodyClasses.add("container-body"),t.containerCls&&e.classes.add(t.containerCls),e._layout=_e.create((t.layout||"")+"layout"),e.settings.items?e.add(e.settings.items):e.add(e.render()),e._hasBody=!0},items:function(){return this._items},find:function(t){return(t=Se[t]=Se[t]||new Ft(t)).find(this)},add:function(t){return this.items().add(this.create(t)).parent(this),this},focus:function(t){var e,n,i,r=this;if(!t||!(n=r.keyboardNav||r.parents().eq(-1)[0].keyboardNav))return i=r.find("*"),r.statusbar&&i.add(r.statusbar.items()),i.each(function(t){if(t.settings.autofocus)return e=null,!1;t.canFocus&&(e=e||t)}),e&&e.focus(),r;n.focusFirst(r)},replace:function(t,e){for(var n,i=this.items(),r=i.length;r--;)if(i[r]===t){i[r]=e;break}0<=r&&((n=e.getEl())&&n.parentNode.removeChild(n),(n=t.getEl())&&n.parentNode.removeChild(n)),e.parent(this)},create:function(t){var e,n=this,i=[];return R.isArray(t)||(t=[t]),R.each(t,function(t){t&&(t instanceof ae||("string"==typeof t&&(t={type:t}),e=R.extend({},n.settings.defaults,t),t.type=e.type=e.type||t.type||n.settings.defaultType||(e.defaults?e.defaults.type:null),t=_e.create(e)),i.push(t))}),i},renderNew:function(){var i=this;return i.items().each(function(t,e){var n;t.parent(i),t.state.get("rendered")||((n=i.getEl("body")).hasChildNodes()&&e<=n.childNodes.length-1?Tt(n.childNodes[e]).before(t.renderHtml()):Tt(n).append(t.renderHtml()),t.postRender(),Zt.add(t))}),i._layout.applyClasses(i.items().filter(":visible")),i._lastRect=null,i},append:function(t){return this.add(t).renderNew()},prepend:function(t){return this.items().set(this.create(t).concat(this.items().toArray())),this.renderNew()},insert:function(t,e,n){var i,r,o;return t=this.create(t),i=this.items(),!n&&e<i.length-1&&(e+=1),0<=e&&e<i.length&&(r=i.slice(0,e).toArray(),o=i.slice(e).toArray(),i.set(r.concat(t,o))),this.renderNew()},fromJSON:function(t){for(var e in t)this.find("#"+e).value(t[e]);return this},toJSON:function(){var i={};return this.find("*").each(function(t){var e=t.name(),n=t.value();e&&void 0!==n&&(i[e]=n)}),i},renderHtml:function(){var t=this,e=t._layout,n=this.settings.role;return t.preRender(),e.preRender(t),'<div id="'+t._id+'" class="'+t.classes+'"'+(n?' role="'+this.settings.role+'"':"")+'><div id="'+t._id+'-body" class="'+t.bodyClasses+'">'+(t.settings.html||"")+e.renderHtml(t)+"</div></div>"},postRender:function(){var t,e=this;return e.items().exec("postRender"),e._super(),e._layout.postRender(e),e.state.set("rendered",!0),e.settings.style&&e.$el.css(e.settings.style),e.settings.border&&(t=e.borderBox,e.$el.css({"border-top-width":t.top,"border-right-width":t.right,"border-bottom-width":t.bottom,"border-left-width":t.left})),e.parent()||(e.keyboardNav=Ce({root:e})),e},initLayoutRect:function(){var t=this._super();return this._layout.recalc(this),t},recalc:function(){var t=this,e=t._layoutRect,n=t._lastRect;if(!n||n.w!==e.w||n.h!==e.h)return t._layout.recalc(t),e=t.layoutRect(),t._lastRect={x:e.x,y:e.y,w:e.w,h:e.h},!0},reflow:function(){var t;if(Zt.remove(this),this.visible()){for(ae.repaintControls=[],ae.repaintControls.map={},this.recalc(),t=ae.repaintControls.length;t--;)ae.repaintControls[t].repaint();"flow"!==this.settings.layout&&"stack"!==this.settings.layout&&this.repaint(),ae.repaintControls=[]}return this}}),Ne={init:function(){this.on("repaint",this.renderScroll)},renderScroll:function(){var p=this,v=2;function n(){var m,g,t;function e(t,e,n,i,r,o){var s,a,l,u,c,d,f,h;if(a=p.getEl("scroll"+t)){if(f=e.toLowerCase(),h=n.toLowerCase(),Tt(p.getEl("absend")).css(f,p.layoutRect()[i]-1),!r)return void Tt(a).css("display","none");Tt(a).css("display","block"),s=p.getEl("body"),l=p.getEl("scroll"+t+"t"),u=s["client"+n]-2*v,c=(u-=m&&g?a["client"+o]:0)/s["scroll"+n],(d={})[f]=s["offset"+e]+v,d[h]=u,Tt(a).css(d),(d={})[f]=s["scroll"+e]*c,d[h]=u*c,Tt(l).css(d)}}t=p.getEl("body"),m=t.scrollWidth>t.clientWidth,g=t.scrollHeight>t.clientHeight,e("h","Left","Width","contentW",m,"Height"),e("v","Top","Height","contentH",g,"Width")}p.settings.autoScroll&&(p._hasScroll||(p._hasScroll=!0,function(){function t(s,a,l,u,c){var d,t=p._id+"-scroll"+s,e=p.classPrefix;Tt(p.getEl()).append('<div id="'+t+'" class="'+e+"scrollbar "+e+"scrollbar-"+s+'"><div id="'+t+'t" class="'+e+'scrollbar-thumb"></div></div>'),p.draghelper=new we(t+"t",{start:function(){d=p.getEl("body")["scroll"+a],Tt("#"+t).addClass(e+"active")},drag:function(t){var e,n,i,r,o=p.layoutRect();n=o.contentW>o.innerW,i=o.contentH>o.innerH,r=p.getEl("body")["client"+l]-2*v,e=(r-=n&&i?p.getEl("scroll"+s)["client"+c]:0)/p.getEl("body")["scroll"+l],p.getEl("body")["scroll"+a]=d+t["delta"+u]/e},stop:function(){Tt("#"+t).removeClass(e+"active")}})}p.classes.add("scroll"),t("v","Top","Height","Y","Width"),t("h","Left","Width","X","Height")}(),p.on("wheel",function(t){var e=p.getEl("body");e.scrollLeft+=10*(t.deltaX||0),e.scrollTop+=10*t.deltaY,n()}),Tt(p.getEl("body")).on("scroll",n)),n())}},Oe=Me.extend({Defaults:{layout:"fit",containerCls:"panel"},Mixins:[Ne],renderHtml:function(){var t=this,e=t._layout,n=t.settings.html;return t.preRender(),e.preRender(t),void 0===n?n='<div id="'+t._id+'-body" class="'+t.bodyClasses+'">'+e.renderHtml(t)+"</div>":("function"==typeof n&&(n=n.call(t)),t._hasBody=!1),'<div id="'+t._id+'" class="'+t.classes+'" hidefocus="1" tabindex="-1" role="group">'+(t._preBodyHtml||"")+n+"</div>"}}),We={resizeToContent:function(){this._layoutRect.autoResize=!0,this._lastRect=null,this.reflow()},resizeTo:function(t,e){if(t<=1||e<=1){var n=Ht.getWindowSize();t=t<=1?t*n.w:t,e=e<=1?e*n.h:e}return this._layoutRect.autoResize=!1,this.layoutRect({minW:t,minH:e,w:t,h:e}).reflow()},resizeBy:function(t,e){var n=this.layoutRect();return this.resizeTo(n.w+t,n.h+e)}},Pe=[],De=[];function Ae(t,e){for(;t;){if(t===e)return!0;t=t.parent()}}function Be(){ke||(ke=function(t){2!==t.button&&function(t){for(var e=Pe.length;e--;){var n=Pe[e],i=n.getParentCtrl(t.target);if(n.settings.autohide){if(i&&(Ae(i,n)||n.parent()===i))continue;(t=n.fire("autohide",{target:t.target})).isDefaultPrevented()||n.hide()}}}(t)},Tt(_.document).on("click touchstart",ke))}function Le(r){var t=Ht.getViewPort().y;function e(t,e){for(var n,i=0;i<Pe.length;i++)if(Pe[i]!==r)for(n=Pe[i].parent();n&&(n=n.parent());)n===r&&Pe[i].fixed(t).moveBy(0,e).repaint()}r.settings.autofix&&(r.state.get("fixed")?r._autoFixY>t&&(r.fixed(!1).layoutRect({y:r._autoFixY}).repaint(),e(!1,r._autoFixY-t)):(r._autoFixY=r.layoutRect().y,r._autoFixY<t&&(r.fixed(!0).layoutRect({y:0}).repaint(),e(!0,t-r._autoFixY))))}function Ie(t,e){var n,i,r=ze.zIndex||65535;if(t)De.push(e);else for(n=De.length;n--;)De[n]===e&&De.splice(n,1);if(De.length)for(n=0;n<De.length;n++)De[n].modal&&(r++,i=De[n]),De[n].getEl().style.zIndex=r,De[n].zIndex=r,r++;var o=Tt("#"+e.classPrefix+"modal-block",e.getContainerElm())[0];i?Tt(o).css("z-index",i.zIndex-1):o&&(o.parentNode.removeChild(o),Te=!1),ze.currentZIndex=r}var ze=Oe.extend({Mixins:[he,We],init:function(t){var i=this;i._super(t),(i._eventsRoot=i).classes.add("floatpanel"),t.autohide&&(Be(),function(){if(!He){var t=_.document.documentElement,e=t.clientWidth,n=t.clientHeight;He=function(){_.document.all&&e===t.clientWidth&&n===t.clientHeight||(e=t.clientWidth,n=t.clientHeight,ze.hideAll())},Tt(_.window).on("resize",He)}}(),Pe.push(i)),t.autofix&&(Ee||(Ee=function(){var t;for(t=Pe.length;t--;)Le(Pe[t])},Tt(_.window).on("scroll",Ee)),i.on("move",function(){Le(this)})),i.on("postrender show",function(t){if(t.control===i){var e,n=i.classPrefix;i.modal&&!Te&&((e=Tt("#"+n+"modal-block",i.getContainerElm()))[0]||(e=Tt('<div id="'+n+'modal-block" class="'+n+"reset "+n+'fade"></div>').appendTo(i.getContainerElm())),c.setTimeout(function(){e.addClass(n+"in"),Tt(i.getEl()).addClass(n+"in")}),Te=!0),Ie(!0,i)}}),i.on("show",function(){i.parents().each(function(t){if(t.state.get("fixed"))return i.fixed(!0),!1})}),t.popover&&(i._preBodyHtml='<div class="'+i.classPrefix+'arrow"></div>',i.classes.add("popover").add("bottom").add(i.isRtl()?"end":"start")),i.aria("label",t.ariaLabel),i.aria("labelledby",i._id),i.aria("describedby",i.describedBy||i._id+"-none")},fixed:function(t){var e=this;if(e.state.get("fixed")!==t){if(e.state.get("rendered")){var n=Ht.getViewPort();t?e.layoutRect().y-=n.y:e.layoutRect().y+=n.y}e.classes.toggle("fixed",t),e.state.set("fixed",t)}return e},show:function(){var t,e=this._super();for(t=Pe.length;t--&&Pe[t]!==this;);return-1===t&&Pe.push(this),e},hide:function(){return Fe(this),Ie(!1,this),this._super()},hideAll:function(){ze.hideAll()},close:function(){return this.fire("close").isDefaultPrevented()||(this.remove(),Ie(!1,this)),this},remove:function(){Fe(this),this._super()},postRender:function(){return this.settings.bodyRole&&this.getEl("body").setAttribute("role",this.settings.bodyRole),this._super()}});function Fe(t){var e;for(e=Pe.length;e--;)Pe[e]===t&&Pe.splice(e,1);for(e=De.length;e--;)De[e]===t&&De.splice(e,1)}ze.hideAll=function(){for(var t=Pe.length;t--;){var e=Pe[t];e&&e.settings.autohide&&(e.hide(),Pe.splice(t,1))}};var Ue=[],Ve="";function qe(t){var e,n=Tt("meta[name=viewport]")[0];!1!==h.overrideViewPort&&(n||((n=_.document.createElement("meta")).setAttribute("name","viewport"),_.document.getElementsByTagName("head")[0].appendChild(n)),(e=n.getAttribute("content"))&&void 0!==Ve&&(Ve=e),n.setAttribute("content",t?"width=device-width,initial-scale=1.0,user-scalable=0,minimum-scale=1.0,maximum-scale=1.0":Ve))}function Ye(t,e){(function(){for(var t=0;t<Ue.length;t++)if(Ue[t]._fullscreen)return!0;return!1})()&&!1===e&&Tt([_.document.documentElement,_.document.body]).removeClass(t+"fullscreen")}var $e=ze.extend({modal:!0,Defaults:{border:1,layout:"flex",containerCls:"panel",role:"dialog",callbacks:{submit:function(){this.fire("submit",{data:this.toJSON()})},close:function(){this.close()}}},init:function(t){var n=this;n._super(t),n.isRtl()&&n.classes.add("rtl"),n.classes.add("window"),n.bodyClasses.add("window-body"),n.state.set("fixed",!0),t.buttons&&(n.statusbar=new Oe({layout:"flex",border:"1 0 0 0",spacing:3,padding:10,align:"center",pack:n.isRtl()?"start":"end",defaults:{type:"button"},items:t.buttons}),n.statusbar.classes.add("foot"),n.statusbar.parent(n)),n.on("click",function(t){var e=n.classPrefix+"close";(Ht.hasClass(t.target,e)||Ht.hasClass(t.target.parentNode,e))&&n.close()}),n.on("cancel",function(){n.close()}),n.on("move",function(t){t.control===n&&ze.hideAll()}),n.aria("describedby",n.describedBy||n._id+"-none"),n.aria("label",t.title),n._fullscreen=!1},recalc:function(){var t,e,n,i,r=this,o=r.statusbar;r._fullscreen&&(r.layoutRect(Ht.getWindowSize()),r.layoutRect().contentH=r.layoutRect().innerH),r._super(),t=r.layoutRect(),r.settings.title&&!r._fullscreen&&(e=t.headerW)>t.w&&(n=t.x-Math.max(0,e/2),r.layoutRect({w:e,x:n}),i=!0),o&&(o.layoutRect({w:r.layoutRect().innerW}).recalc(),(e=o.layoutRect().minW+t.deltaW)>t.w&&(n=t.x-Math.max(0,e-t.w),r.layoutRect({w:e,x:n}),i=!0)),i&&r.recalc()},initLayoutRect:function(){var t,e=this,n=e._super(),i=0;if(e.settings.title&&!e._fullscreen){t=e.getEl("head");var r=Ht.getSize(t);n.headerW=r.width,n.headerH=r.height,i+=n.headerH}e.statusbar&&(i+=e.statusbar.layoutRect().h),n.deltaH+=i,n.minH+=i,n.h+=i;var o=Ht.getWindowSize();return n.x=e.settings.x||Math.max(0,o.w/2-n.w/2),n.y=e.settings.y||Math.max(0,o.h/2-n.h/2),n},renderHtml:function(){var t=this,e=t._layout,n=t._id,i=t.classPrefix,r=t.settings,o="",s="",a=r.html;return t.preRender(),e.preRender(t),r.title&&(o='<div id="'+n+'-head" class="'+i+'window-head"><div id="'+n+'-title" class="'+i+'title">'+t.encode(r.title)+'</div><div id="'+n+'-dragh" class="'+i+'dragh"></div><button type="button" class="'+i+'close" aria-hidden="true"><i class="mce-ico mce-i-remove"></i></button></div>'),r.url&&(a='<iframe src="'+r.url+'" tabindex="-1"></iframe>'),void 0===a&&(a=e.renderHtml(t)),t.statusbar&&(s=t.statusbar.renderHtml()),'<div id="'+n+'" class="'+t.classes+'" hidefocus="1"><div class="'+t.classPrefix+'reset" role="application">'+o+'<div id="'+n+'-body" class="'+t.bodyClasses+'">'+a+"</div>"+s+"</div></div>"},fullscreen:function(t){var n,e,i=this,r=_.document.documentElement,o=i.classPrefix;if(t!==i._fullscreen)if(Tt(_.window).on("resize",function(){var t;if(i._fullscreen)if(n)i._timer||(i._timer=c.setTimeout(function(){var t=Ht.getWindowSize();i.moveTo(0,0).resizeTo(t.w,t.h),i._timer=0},50));else{t=(new Date).getTime();var e=Ht.getWindowSize();i.moveTo(0,0).resizeTo(e.w,e.h),50<(new Date).getTime()-t&&(n=!0)}}),e=i.layoutRect(),i._fullscreen=t){i._initial={x:e.x,y:e.y,w:e.w,h:e.h},i.borderBox=Nt("0"),i.getEl("head").style.display="none",e.deltaH-=e.headerH+2,Tt([r,_.document.body]).addClass(o+"fullscreen"),i.classes.add("fullscreen");var s=Ht.getWindowSize();i.moveTo(0,0).resizeTo(s.w,s.h)}else i.borderBox=Nt(i.settings.border),i.getEl("head").style.display="",e.deltaH+=e.headerH,Tt([r,_.document.body]).removeClass(o+"fullscreen"),i.classes.remove("fullscreen"),i.moveTo(i._initial.x,i._initial.y).resizeTo(i._initial.w,i._initial.h);return i.reflow()},postRender:function(){var e,n=this;setTimeout(function(){n.classes.add("in"),n.fire("open")},0),n._super(),n.statusbar&&n.statusbar.postRender(),n.focus(),this.dragHelper=new we(n._id+"-dragh",{start:function(){e={x:n.layoutRect().x,y:n.layoutRect().y}},drag:function(t){n.moveTo(e.x+t.deltaX,e.y+t.deltaY)}}),n.on("submit",function(t){t.isDefaultPrevented()||n.close()}),Ue.push(n),qe(!0)},submit:function(){return this.fire("submit",{data:this.toJSON()})},remove:function(){var t,e=this;for(e.dragHelper.destroy(),e._super(),e.statusbar&&this.statusbar.remove(),Ye(e.classPrefix,!1),t=Ue.length;t--;)Ue[t]===e&&Ue.splice(t,1);qe(0<Ue.length)},getContentWindow:function(){var t=this.getEl().getElementsByTagName("iframe")[0];return t?t.contentWindow:null}});!function(){if(!h.desktop){var n={w:_.window.innerWidth,h:_.window.innerHeight};c.setInterval(function(){var t=_.window.innerWidth,e=_.window.innerHeight;n.w===t&&n.h===e||(n={w:t,h:e},Tt(_.window).trigger("resize"))},100)}Tt(_.window).on("resize",function(){var t,e,n=Ht.getWindowSize();for(t=0;t<Ue.length;t++)e=Ue[t].layoutRect(),Ue[t].moveTo(Ue[t].settings.x||Math.max(0,n.w/2-e.w/2),Ue[t].settings.y||Math.max(0,n.h/2-e.h/2))})}();var Xe,je,Je,Ge=$e.extend({init:function(t){t={border:1,padding:20,layout:"flex",pack:"center",align:"center",containerCls:"panel",autoScroll:!0,buttons:{type:"button",text:"Ok",action:"ok"},items:{type:"label",multiline:!0,maxWidth:500,maxHeight:200}},this._super(t)},Statics:{OK:1,OK_CANCEL:2,YES_NO:3,YES_NO_CANCEL:4,msgBox:function(t){var e,i=t.callback||function(){};function n(t,e,n){return{type:"button",text:t,subtype:n?"primary":"",onClick:function(t){t.control.parents()[1].close(),i(e)}}}switch(t.buttons){case Ge.OK_CANCEL:e=[n("Ok",!0,!0),n("Cancel",!1)];break;case Ge.YES_NO:case Ge.YES_NO_CANCEL:e=[n("Yes",1,!0),n("No",0)],t.buttons===Ge.YES_NO_CANCEL&&e.push(n("Cancel",-1));break;default:e=[n("Ok",!0,!0)]}return new $e({padding:20,x:t.x,y:t.y,minWidth:300,minHeight:100,layout:"flex",pack:"center",align:"center",buttons:e,title:t.title,role:"alertdialog",items:{type:"label",multiline:!0,maxWidth:500,maxHeight:200,text:t.text},onPostRender:function(){this.aria("describedby",this.items()[0]._id)},onClose:t.onClose,onCancel:function(){i(!1)}}).renderTo(_.document.body).reflow()},alert:function(t,e){return"string"==typeof t&&(t={text:t}),t.callback=e,Ge.msgBox(t)},confirm:function(t,e){return"string"==typeof t&&(t={text:t}),t.callback=e,t.buttons=Ge.OK_CANCEL,Ge.msgBox(t)}}}),Ke=function(t,e){return{renderUI:function(){return st(t,e)},getNotificationManagerImpl:function(){return ye(t)},getWindowManagerImpl:function(){return{open:function(n,t,e){var i;return n.title=n.title||" ",n.url=n.url||n.file,n.url&&(n.width=parseInt(n.width||320,10),n.height=parseInt(n.height||240,10)),n.body&&(n.items={defaults:n.defaults,type:n.bodyType||"form",items:n.body,data:n.data,callbacks:n.commands}),n.url||n.buttons||(n.buttons=[{text:"Ok",subtype:"primary",onclick:function(){i.find("form")[0].submit()}},{text:"Cancel",onclick:function(){i.close()}}]),(i=new $e(n)).on("close",function(){e(i)}),n.data&&i.on("postRender",function(){this.find("*").each(function(t){var e=t.name();e in n.data&&t.value(n.data[e])})}),i.features=n||{},i.params=t||{},i=i.renderTo(_.document.body).reflow()},alert:function(t,e,n){var i;return(i=Ge.alert(t,function(){e()})).on("close",function(){n(i)}),i},confirm:function(t,e,n){var i;return(i=Ge.confirm(t,function(t){e(t)})).on("close",function(){n(i)}),i},close:function(t){t.close()},getParams:function(t){return t.params},setParams:function(t,e){t.params=e}}}}},Ze="undefined"!=typeof _.window?_.window:Function("return this;")(),Qe=function(t,e){return function(t,e){for(var n=e!==undefined&&null!==e?e:Ze,i=0;i<t.length&&n!==undefined&&null!==n;++i)n=n[t[i]];return n}(t.split("."),e)},tn=function(t,e){var n=Qe(t,e);if(n===undefined||null===n)throw new Error(t+" not available on this browser");return n},en=tinymce.util.Tools.resolve("tinymce.util.Promise"),nn=function(n){return new en(function(t){var e=new(tn("FileReader"));e.onloadend=function(){t(e.result.split(",")[1])},e.readAsDataURL(n)})},rn=function(){return new en(function(e){var t;(t=_.document.createElement("input")).type="file",t.style.position="fixed",t.style.left=0,t.style.top=0,t.style.opacity=.001,_.document.body.appendChild(t),t.onchange=function(t){e(Array.prototype.slice.call(t.target.files))},t.click(),t.parentNode.removeChild(t)})},on=0,sn=function(t){return t+on+++(e=function(){return Math.round(4294967295*Math.random()).toString(36)},"s"+Date.now().toString(36)+e()+e()+e());var e},an=function(r,o){var s={};function t(t){var e,n,i;n=o[t?"startContainer":"endContainer"],i=o[t?"startOffset":"endOffset"],1===n.nodeType&&(e=r.create("span",{"data-mce-type":"bookmark"}),n.hasChildNodes()?(i=Math.min(i,n.childNodes.length-1),t?n.insertBefore(e,n.childNodes[i]):r.insertAfter(e,n.childNodes[i])):n.appendChild(e),n=e,i=0),s[t?"startContainer":"endContainer"]=n,s[t?"startOffset":"endOffset"]=i}return t(!0),o.collapsed||t(),s},ln=function(r,o){function t(t){var e,n,i;e=i=o[t?"startContainer":"endContainer"],n=o[t?"startOffset":"endOffset"],e&&(1===e.nodeType&&(n=function(t){for(var e=t.parentNode.firstChild,n=0;e;){if(e===t)return n;1===e.nodeType&&"bookmark"===e.getAttribute("data-mce-type")||n++,e=e.nextSibling}return-1}(e),e=e.parentNode,r.remove(i)),o[t?"startContainer":"endContainer"]=e,o[t?"startOffset":"endOffset"]=n)}t(!0),t();var e=r.createRng();return e.setStart(o.startContainer,o.startOffset),o.endContainer&&e.setEnd(o.endContainer,o.endOffset),e},un=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),cn=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),dn=function(t){return"A"===t.nodeName&&t.hasAttribute("href")},fn=function(t){var e,n,i,r,o,s,a,l;return r=t.selection,o=t.dom,s=r.getRng(),a=o,l=cn.getNode(s.startContainer,s.startOffset),e=a.getParent(l,dn)||l,n=cn.getNode(s.endContainer,s.endOffset),i=t.getBody(),R.grep(function(t,e,n){var i,r,o=[];for(i=new un(e,t),r=e;r&&(1===r.nodeType&&o.push(r),r!==n);r=i.next());return o}(i,e,n),dn)},hn=function(t){var e,n,i,r,o;n=fn(e=t),r=e.dom,o=e.selection,i=an(r,o.getRng()),R.each(n,function(t){e.dom.remove(t,!0)}),o.setRng(ln(r,i))},mn=function(t){t.selection.collapse(!1)},gn=function(t){t.focus(),hn(t),mn(t)},pn=function(t,e){var n,i,r,o,s,a=t.dom.getParent(t.selection.getStart(),"a[href]");a?(o=a,s=e,(r=t).focus(),r.dom.setAttrib(o,"href",s),mn(r)):(i=e,(n=t).execCommand("mceInsertLink",!1,{href:i}),mn(n))},vn=function(t,e,n){var i,r,o;t.plugins.table?t.plugins.table.insertTable(e,n):(r=e,o=n,(i=t).undoManager.transact(function(){var t,e;i.insertContent(function(t,e){var n,i,r;for(r='<table data-mce-id="mce" style="width: 100%">',r+="<tbody>",i=0;i<e;i++){for(r+="<tr>",n=0;n<t;n++)r+="<td><br></td>";r+="</tr>"}return r+="</tbody>",r+="</table>"}(r,o)),(t=i.dom.select("*[data-mce-id]")[0]).removeAttribute("data-mce-id"),e=i.dom.select("td,th",t),i.selection.setCursorLocation(e[0],0)}))},bn=function(t,e){t.execCommand("FormatBlock",!1,e)},yn=function(t,e,n){var i,r;r=(i=t.editorUpload.blobCache).create(sn("mceu"),n,e),i.add(r),t.insertContent(t.dom.createHTML("img",{src:r.blobUri()}))},xn=function(t,e){0===e.trim().length?gn(t):pn(t,e)},wn=gn,_n=function(n,t){n.addButton("quicklink",{icon:"link",tooltip:"Insert/Edit link",stateSelector:"a[href]",onclick:function(){t.showForm(n,"quicklink")}}),n.addButton("quickimage",{icon:"image",tooltip:"Insert image",onclick:function(){rn().then(function(t){var e=t[0];nn(e).then(function(t){yn(n,t,e)})})}}),n.addButton("quicktable",{icon:"table",tooltip:"Insert table",onclick:function(){t.hide(),vn(n,2,2)}}),function(e){for(var t=function(t){return function(){bn(e,t)}},n=1;n<6;n++){var i="h"+n;e.addButton(i,{text:i.toUpperCase(),tooltip:"Heading "+n,stateSelector:i,onclick:t(i),onPostRender:function(){this.getEl().firstChild.firstChild.style.fontWeight="bold"}})}}(n)},Rn=function(){var t=h.container;if(t&&"static"!==v.DOM.getStyle(t,"position",!0)){var e=v.DOM.getPos(t),n=e.x-t.scrollLeft,i=e.y-t.scrollTop;return mt.some({x:n,y:i})}return mt.none()},Cn=function(t){return/^www\.|\.(com|org|edu|gov|uk|net|ca|de|jp|fr|au|us|ru|ch|it|nl|se|no|es|mil)$/i.test(t.trim())},kn=function(t){return/^https?:\/\//.test(t.trim())},En=function(t,e){return!kn(e)&&Cn(e)?(n=t,i=e,new en(function(e){n.windowManager.confirm("The URL you entered seems to be an external link. Do you want to add the required http:// prefix?",function(t){e(!0===t?"http://"+i:i)})})):en.resolve(e);var n,i},Hn=function(r,e){var t,n,i,o={};return t="quicklink",n={items:[{type:"button",name:"unlink",icon:"unlink",onclick:function(){r.focus(),wn(r),e()},tooltip:"Remove link"},{type:"filepicker",name:"linkurl",placeholder:"Paste or type a link",filetype:"file",onchange:function(t){var e=t.meta;e&&e.attach&&(o={href:this.value(),attach:e.attach})}},{type:"button",icon:"checkmark",subtype:"primary",tooltip:"Ok",onclick:"submit"}],onshow:function(t){if(t.control===this){var e,n="";(e=r.dom.getParent(r.selection.getStart(),"a[href]"))&&(n=r.dom.getAttrib(e,"href")),this.fromJSON({linkurl:n}),i=this.find("#unlink"),e?i.show():i.hide(),this.find("#linkurl")[0].focus()}var i},onsubmit:function(t){En(r,t.data.linkurl).then(function(t){r.undoManager.transact(function(){t===o.href&&(o.attach(),o={}),xn(r,t)}),e()})}},(i=_e.create(R.extend({type:"form",layout:"flex",direction:"row",padding:5,name:t,spacing:3},n))).on("show",function(){i.find("textbox").eq(0).each(function(t){t.focus()})}),i},Tn=function(n,t,e){var o,i,s=[];if(e)return R.each(B(i=e)?i:W(i)?i.split(/[ ,]/):[],function(t){if("|"===t)o=null;else if(n.buttons[t]){o||(o={type:"buttongroup",items:[]},s.push(o));var e=n.buttons[t];A(e)&&(e=e()),e.type=e.type||"button",(e=_e.create(e)).on("postRender",(i=n,r=e,function(){var e,t,n=(t=function(t,e){return{selector:t,handler:e}},(e=r).settings.stateSelector?t(e.settings.stateSelector,function(t){e.active(t)}):e.settings.disabledStateSelector?t(e.settings.disabledStateSelector,function(t){e.disabled(t)}):null);null!==n&&i.selection.selectorChanged(n.selector,n.handler)})),o.items.push(e)}var i,r}),_e.create({type:"toolbar",layout:"flow",name:t,items:s})},Sn=function(){var l,c,o=function(t){return 0<t.items().length},u=function(t,e){var n,i,r=(n=t,i=e,R.map(i,function(t){return Tn(n,t.id,t.items)})).concat([Tn(t,"text",J(t)),Tn(t,"insert",G(t)),Hn(t,p)]);return _e.create({type:"floatpanel",role:"dialog",classes:"tinymce tinymce-inline arrow",ariaLabel:"Inline toolbar",layout:"flex",direction:"column",align:"stretch",autohide:!1,autofix:!0,fixed:!0,border:1,items:R.grep(r,o),oncancel:function(){t.focus()}})},d=function(t){t&&t.show()},f=function(t,e){t.moveTo(e.x,e.y)},h=function(n,i){i=i?i.substr(0,2):"",R.each({t:"down",b:"up",c:"center"},function(t,e){n.classes.toggle("arrow-"+t,e===i.substr(0,1))}),"cr"===i?(n.classes.toggle("arrow-left",!0),n.classes.toggle("arrow-right",!1)):"cl"===i?(n.classes.toggle("arrow-left",!1),n.classes.toggle("arrow-right",!0)):R.each({l:"left",r:"right"},function(t,e){n.classes.toggle("arrow-"+t,e===i.substr(1,1))})},m=function(t,e){var n=t.items().filter("#"+e);return 0<n.length&&(n[0].show(),t.reflow(),!0)},g=function(t,e,n,i){var r,o,s,a;if(a=K(n),r=b(n),o=v.DOM.getRect(t.getEl()),s="insert"===e?Y(i,r,o):$(i,r,o)){var l=Rn().getOr({x:0,y:0}),u={x:s.rect.x-l.x,y:s.rect.y-l.y,w:s.rect.w,h:s.rect.h};return f(t,X(a,c=i,r,u)),h(t,s.position),!0}return!1},p=function(){l&&l.hide()};return{show:function(t,e,n,i){var r,o,s,a;l||(S(t),(l=u(t,i)).renderTo().reflow().moveTo(n.x,n.y),t.nodeChanged()),o=e,s=t,a=n,d(r=l),r.items().hide(),m(r,o)?!1===g(r,o,s,a)&&p():p()},showForm:function(t,e){if(l){if(l.items().hide(),!m(l,e))return void p();var n,i,r,o=void 0;d(l),l.items().hide(),m(l,e),r=K(t),n=b(t),o=v.DOM.getRect(l.getEl()),(i=$(c,n,o))&&(o=i.rect,f(l,X(r,c,n,o)),h(l,i.position))}},reposition:function(t,e,n){l&&g(l,e,t,n)},inForm:function(){return l&&l.visible()&&0<l.items().filter("form:visible").length},hide:p,focus:function(){l&&l.find("toolbar:visible").eq(0).each(function(t){t.focus(!0)})},remove:function(){l&&(l.remove(),l=null)}}},Mn=St.extend({Defaults:{firstControlClass:"first",lastControlClass:"last"},init:function(t){this.settings=R.extend({},this.Defaults,t)},preRender:function(t){t.bodyClasses.add(this.settings.containerClass)},applyClasses:function(t){var e,n,i,r,o=this.settings;e=o.firstControlClass,n=o.lastControlClass,t.each(function(t){t.classes.remove(e).remove(n).add(o.controlClass),t.visible()&&(i||(i=t),r=t)}),i&&i.classes.add(e),r&&r.classes.add(n)},renderHtml:function(t){var e="";return this.applyClasses(t.items()),t.items().each(function(t){e+=t.renderHtml()}),e},recalc:function(){},postRender:function(){},isNative:function(){return!1}}),Nn=Mn.extend({Defaults:{containerClass:"abs-layout",controlClass:"abs-layout-item"},recalc:function(t){t.items().filter(":visible").each(function(t){var e=t.settings;t.layoutRect({x:e.x,y:e.y,w:e.w,h:e.h}),t.recalc&&t.recalc()})},renderHtml:function(t){return'<div id="'+t._id+'-absend" class="'+t.classPrefix+'abs-end"></div>'+this._super(t)}}),On=ge.extend({Defaults:{classes:"widget btn",role:"button"},init:function(t){var e,n=this;n._super(t),t=n.settings,e=n.settings.size,n.on("click mousedown",function(t){t.preventDefault()}),n.on("touchstart",function(t){n.fire("click",t),t.preventDefault()}),t.subtype&&n.classes.add(t.subtype),e&&n.classes.add("btn-"+e),t.icon&&n.icon(t.icon)},icon:function(t){return arguments.length?(this.state.set("icon",t),this):this.state.get("icon")},repaint:function(){var t,e=this.getEl().firstChild;e&&((t=e.style).width=t.height="100%"),this._super()},renderHtml:function(){var t,e,n=this,i=n._id,r=n.classPrefix,o=n.state.get("icon"),s=n.state.get("text"),a="",l=n.settings;return(t=l.image)?(o="none","string"!=typeof t&&(t=_.window.getSelection?t[0]:t[1]),t=" style=\"background-image: url('"+t+"')\""):t="",s&&(n.classes.add("btn-has-text"),a='<span class="'+r+'txt">'+n.encode(s)+"</span>"),o=o?r+"ico "+r+"i-"+o:"",e="boolean"==typeof l.active?' aria-pressed="'+l.active+'"':"",'<div id="'+i+'" class="'+n.classes+'" tabindex="-1"'+e+'><button id="'+i+'-button" role="presentation" type="button" tabindex="-1">'+(o?'<i class="'+o+'"'+t+"></i>":"")+a+"</button></div>"},bindStates:function(){var o=this,n=o.$,i=o.classPrefix+"txt";function s(t){var e=n("span."+i,o.getEl());t?(e[0]||(n("button:first",o.getEl()).append('<span class="'+i+'"></span>'),e=n("span."+i,o.getEl())),e.html(o.encode(t))):e.remove(),o.classes.toggle("btn-has-text",!!t)}return o.state.on("change:text",function(t){s(t.value)}),o.state.on("change:icon",function(t){var e=t.value,n=o.classPrefix;e=(o.settings.icon=e)?n+"ico "+n+"i-"+o.settings.icon:"";var i=o.getEl().firstChild,r=i.getElementsByTagName("i")[0];e?(r&&r===i.firstChild||(r=_.document.createElement("i"),i.insertBefore(r,i.firstChild)),r.className=e):r&&i.removeChild(r),s(o.state.get("text"))}),o._super()}}),Wn=On.extend({init:function(t){t=R.extend({text:"Browse...",multiple:!1,accept:null},t),this._super(t),this.classes.add("browsebutton"),t.multiple&&this.classes.add("multiple")},postRender:function(){var n=this,e=Ht.create("input",{type:"file",id:n._id+"-browse",accept:n.settings.accept});n._super(),Tt(e).on("change",function(t){var e=t.target.files;n.value=function(){return e.length?n.settings.multiple?e:e[0]:null},t.preventDefault(),e.length&&n.fire("change",t)}),Tt(e).on("click",function(t){t.stopPropagation()}),Tt(n.getEl("button")).on("click touchstart",function(t){t.stopPropagation(),e.click(),t.preventDefault()}),n.getEl().appendChild(e)},remove:function(){Tt(this.getEl("button")).off(),Tt(this.getEl("input")).off(),this._super()}}),Pn=Me.extend({Defaults:{defaultType:"button",role:"group"},renderHtml:function(){var t=this,e=t._layout;return t.classes.add("btn-group"),t.preRender(),e.preRender(t),'<div id="'+t._id+'" class="'+t.classes+'"><div id="'+t._id+'-body">'+(t.settings.html||"")+e.renderHtml(t)+"</div></div>"}}),Dn=ge.extend({Defaults:{classes:"checkbox",role:"checkbox",checked:!1},init:function(t){var e=this;e._super(t),e.on("click mousedown",function(t){t.preventDefault()}),e.on("click",function(t){t.preventDefault(),e.disabled()||e.checked(!e.checked())}),e.checked(e.settings.checked)},checked:function(t){return arguments.length?(this.state.set("checked",t),this):this.state.get("checked")},value:function(t){return arguments.length?this.checked(t):this.checked()},renderHtml:function(){var t=this,e=t._id,n=t.classPrefix;return'<div id="'+e+'" class="'+t.classes+'" unselectable="on" aria-labelledby="'+e+'-al" tabindex="-1"><i class="'+n+"ico "+n+'i-checkbox"></i><span id="'+e+'-al" class="'+n+'label">'+t.encode(t.state.get("text"))+"</span></div>"},bindStates:function(){var o=this;function e(t){o.classes.toggle("checked",t),o.aria("checked",t)}return o.state.on("change:text",function(t){o.getEl("al").firstChild.data=o.translate(t.value)}),o.state.on("change:checked change:value",function(t){o.fire("change"),e(t.value)}),o.state.on("change:icon",function(t){var e=t.value,n=o.classPrefix;if(void 0===e)return o.settings.icon;e=(o.settings.icon=e)?n+"ico "+n+"i-"+o.settings.icon:"";var i=o.getEl().firstChild,r=i.getElementsByTagName("i")[0];e?(r&&r===i.firstChild||(r=_.document.createElement("i"),i.insertBefore(r,i.firstChild)),r.className=e):r&&i.removeChild(r)}),o.state.get("checked")&&e(!0),o._super()}}),An=tinymce.util.Tools.resolve("tinymce.util.VK"),Bn=ge.extend({init:function(i){var r=this;r._super(i),i=r.settings,r.classes.add("combobox"),r.subinput=!0,r.ariaTarget="inp",i.menu=i.menu||i.values,i.menu&&(i.icon="caret"),r.on("click",function(t){var e=t.target,n=r.getEl();if(Tt.contains(n,e)||e===n)for(;e&&e!==n;)e.id&&-1!==e.id.indexOf("-open")&&(r.fire("action"),i.menu&&(r.showMenu(),t.aria&&r.menu.items()[0].focus())),e=e.parentNode}),r.on("keydown",function(t){var e;13===t.keyCode&&"INPUT"===t.target.nodeName&&(t.preventDefault(),r.parents().reverse().each(function(t){if(t.toJSON)return e=t,!1}),r.fire("submit",{data:e.toJSON()}))}),r.on("keyup",function(t){if("INPUT"===t.target.nodeName){var e=r.state.get("value"),n=t.target.value;n!==e&&(r.state.set("value",n),r.fire("autocomplete",t))}}),r.on("mouseover",function(t){var e=r.tooltip().moveTo(-65535);if(r.statusLevel()&&-1!==t.target.className.indexOf(r.classPrefix+"status")){var n=r.statusMessage()||"Ok",i=e.text(n).show().testMoveRel(t.target,["bc-tc","bc-tl","bc-tr"]);e.classes.toggle("tooltip-n","bc-tc"===i),e.classes.toggle("tooltip-nw","bc-tl"===i),e.classes.toggle("tooltip-ne","bc-tr"===i),e.moveRel(t.target,i)}})},statusLevel:function(t){return 0<arguments.length&&this.state.set("statusLevel",t),this.state.get("statusLevel")},statusMessage:function(t){return 0<arguments.length&&this.state.set("statusMessage",t),this.state.get("statusMessage")},showMenu:function(){var t,e=this,n=e.settings;e.menu||((t=n.menu||[]).length?t={type:"menu",items:t}:t.type=t.type||"menu",e.menu=_e.create(t).parent(e).renderTo(e.getContainerElm()),e.fire("createmenu"),e.menu.reflow(),e.menu.on("cancel",function(t){t.control===e.menu&&e.focus()}),e.menu.on("show hide",function(t){t.control.items().each(function(t){t.active(t.value()===e.value())})}).fire("show"),e.menu.on("select",function(t){e.value(t.control.value())}),e.on("focusin",function(t){"INPUT"===t.target.tagName.toUpperCase()&&e.menu.hide()}),e.aria("expanded",!0)),e.menu.show(),e.menu.layoutRect({w:e.layoutRect().w}),e.menu.moveRel(e.getEl(),e.isRtl()?["br-tr","tr-br"]:["bl-tl","tl-bl"])},focus:function(){this.getEl("inp").focus()},repaint:function(){var t,e,n=this,i=n.getEl(),r=n.getEl("open"),o=n.layoutRect(),s=0,a=i.firstChild;n.statusLevel()&&"none"!==n.statusLevel()&&(s=parseInt(Ht.getRuntimeStyle(a,"padding-right"),10)-parseInt(Ht.getRuntimeStyle(a,"padding-left"),10)),t=r?o.w-Ht.getSize(r).width-10:o.w-10;var l=_.document;return l.all&&(!l.documentMode||l.documentMode<=8)&&(e=n.layoutRect().h-2+"px"),Tt(a).css({width:t-s,lineHeight:e}),n._super(),n},postRender:function(){var e=this;return Tt(this.getEl("inp")).on("change",function(t){e.state.set("value",t.target.value),e.fire("change",t)}),e._super()},renderHtml:function(){var t,e,n,i=this,r=i._id,o=i.settings,s=i.classPrefix,a=i.state.get("value")||"",l="",u="";return"spellcheck"in o&&(u+=' spellcheck="'+o.spellcheck+'"'),o.maxLength&&(u+=' maxlength="'+o.maxLength+'"'),o.size&&(u+=' size="'+o.size+'"'),o.subtype&&(u+=' type="'+o.subtype+'"'),n='<i id="'+r+'-status" class="mce-status mce-ico" style="display: none"></i>',i.disabled()&&(u+=' disabled="disabled"'),(t=o.icon)&&"caret"!==t&&(t=s+"ico "+s+"i-"+o.icon),e=i.state.get("text"),(t||e)&&(l='<div id="'+r+'-open" class="'+s+"btn "+s+'open" tabIndex="-1" role="button"><button id="'+r+'-action" type="button" hidefocus="1" tabindex="-1">'+("caret"!==t?'<i class="'+t+'"></i>':'<i class="'+s+'caret"></i>')+(e?(t?" ":"")+e:"")+"</button></div>",i.classes.add("has-open")),'<div id="'+r+'" class="'+i.classes+'"><input id="'+r+'-inp" class="'+s+'textbox" value="'+i.encode(a,!1)+'" hidefocus="1"'+u+' placeholder="'+i.encode(o.placeholder)+'" />'+n+l+"</div>"},value:function(t){return arguments.length?(this.state.set("value",t),this):(this.state.get("rendered")&&this.state.set("value",this.getEl("inp").value),this.state.get("value"))},showAutoComplete:function(t,i){var r=this;if(0!==t.length){r.menu?r.menu.items().remove():r.menu=_e.create({type:"menu",classes:"combobox-menu",layout:"flow"}).parent(r).renderTo(),R.each(t,function(t){var e,n;r.menu.add({text:t.title,url:t.previewUrl,match:i,classes:"menu-item-ellipsis",onclick:(e=t.value,n=t.title,function(){r.fire("selectitem",{title:n,value:e})})})}),r.menu.renderNew(),r.hideMenu(),r.menu.on("cancel",function(t){t.control.parent()===r.menu&&(t.stopPropagation(),r.focus(),r.hideMenu())}),r.menu.on("select",function(){r.focus()});var e=r.layoutRect().w;r.menu.layoutRect({w:e,minW:0,maxW:e}),r.menu.repaint(),r.menu.reflow(),r.menu.show(),r.menu.moveRel(r.getEl(),r.isRtl()?["br-tr","tr-br"]:["bl-tl","tl-bl"])}else r.hideMenu()},hideMenu:function(){this.menu&&this.menu.hide()},bindStates:function(){var r=this;r.state.on("change:value",function(t){r.getEl("inp").value!==t.value&&(r.getEl("inp").value=t.value)}),r.state.on("change:disabled",function(t){r.getEl("inp").disabled=t.value}),r.state.on("change:statusLevel",function(t){var e=r.getEl("status"),n=r.classPrefix,i=t.value;Ht.css(e,"display","none"===i?"none":""),Ht.toggleClass(e,n+"i-checkmark","ok"===i),Ht.toggleClass(e,n+"i-warning","warn"===i),Ht.toggleClass(e,n+"i-error","error"===i),r.classes.toggle("has-status","none"!==i),r.repaint()}),Ht.on(r.getEl("status"),"mouseleave",function(){r.tooltip().hide()}),r.on("cancel",function(t){r.menu&&r.menu.visible()&&(t.stopPropagation(),r.hideMenu())});var n=function(t,e){e&&0<e.items().length&&e.items().eq(t)[0].focus()};return r.on("keydown",function(t){var e=t.keyCode;"INPUT"===t.target.nodeName&&(e===An.DOWN?(t.preventDefault(),r.fire("autocomplete"),n(0,r.menu)):e===An.UP&&(t.preventDefault(),n(-1,r.menu)))}),r._super()},remove:function(){Tt(this.getEl("inp")).off(),this.menu&&this.menu.remove(),this._super()}}),Ln=Bn.extend({init:function(t){var e=this;t.spellcheck=!1,t.onaction&&(t.icon="none"),e._super(t),e.classes.add("colorbox"),e.on("change keyup postrender",function(){e.repaintColor(e.value())})},repaintColor:function(t){var e=this.getEl("open"),n=e?e.getElementsByTagName("i")[0]:null;if(n)try{n.style.background=t}catch(i){}},bindStates:function(){var e=this;return e.state.on("change:value",function(t){e.state.get("rendered")&&e.repaintColor(t.value)}),e._super()}}),In=On.extend({showPanel:function(){var e=this,t=e.settings;if(e.classes.add("opened"),e.panel)e.panel.show();else{var n=t.panel;n.type&&(n={layout:"grid",items:n}),n.role=n.role||"dialog",n.popover=!0,n.autohide=!0,n.ariaRoot=!0,e.panel=new ze(n).on("hide",function(){e.classes.remove("opened")}).on("cancel",function(t){t.stopPropagation(),e.focus(),e.hidePanel()}).parent(e).renderTo(e.getContainerElm()),e.panel.fire("show"),e.panel.reflow()}var i=e.panel.testMoveRel(e.getEl(),t.popoverAlign||(e.isRtl()?["bc-tc","bc-tl","bc-tr"]:["bc-tc","bc-tr","bc-tl","tc-bc","tc-br","tc-bl"]));e.panel.classes.toggle("start","l"===i.substr(-1)),e.panel.classes.toggle("end","r"===i.substr(-1));var r="t"===i.substr(0,1);e.panel.classes.toggle("bottom",!r),e.panel.classes.toggle("top",r),e.panel.moveRel(e.getEl(),i)},hidePanel:function(){this.panel&&this.panel.hide()},postRender:function(){var e=this;return e.aria("haspopup",!0),e.on("click",function(t){t.control===e&&(e.panel&&e.panel.visible()?e.hidePanel():(e.showPanel(),e.panel.focus(!!t.aria)))}),e._super()},remove:function(){return this.panel&&(this.panel.remove(),this.panel=null),this._super()}}),zn=v.DOM,Fn=In.extend({init:function(t){this._super(t),this.classes.add("splitbtn"),this.classes.add("colorbutton")},color:function(t){return t?(this._color=t,this.getEl("preview").style.backgroundColor=t,this):this._color},resetColor:function(){return this._color=null,this.getEl("preview").style.backgroundColor=null,this},renderHtml:function(){var t=this,e=t._id,n=t.classPrefix,i=t.state.get("text"),r=t.settings.icon?n+"ico "+n+"i-"+t.settings.icon:"",o=t.settings.image?" style=\"background-image: url('"+t.settings.image+"')\"":"",s="";return i&&(t.classes.add("btn-has-text"),s='<span class="'+n+'txt">'+t.encode(i)+"</span>"),'<div id="'+e+'" class="'+t.classes+'" role="button" tabindex="-1" aria-haspopup="true"><button role="presentation" hidefocus="1" type="button" tabindex="-1">'+(r?'<i class="'+r+'"'+o+"></i>":"")+'<span id="'+e+'-preview" class="'+n+'preview"></span>'+s+'</button><button type="button" class="'+n+'open" hidefocus="1" tabindex="-1"> <i class="'+n+'caret"></i></button></div>'},postRender:function(){var e=this,n=e.settings.onclick;return e.on("click",function(t){t.aria&&"down"===t.aria.key||t.control!==e||zn.getParent(t.target,"."+e.classPrefix+"open")||(t.stopImmediatePropagation(),n.call(e,t))}),delete e.settings.onclick,e._super()}}),Un=tinymce.util.Tools.resolve("tinymce.util.Color"),Vn=ge.extend({Defaults:{classes:"widget colorpicker"},init:function(t){this._super(t)},postRender:function(){var n,i,r,o,s,a=this,l=a.color();function u(t,e){var n,i,r=Ht.getPos(t);return n=e.pageX-r.x,i=e.pageY-r.y,{x:n=Math.max(0,Math.min(n/t.clientWidth,1)),y:i=Math.max(0,Math.min(i/t.clientHeight,1))}}function c(t,e){var n=(360-t.h)/360;Ht.css(r,{top:100*n+"%"}),e||Ht.css(s,{left:t.s+"%",top:100-t.v+"%"}),o.style.background=Un({s:100,v:100,h:t.h}).toHex(),a.color().parse({s:t.s,v:t.v,h:t.h})}function t(t){var e;e=u(o,t),n.s=100*e.x,n.v=100*(1-e.y),c(n),a.fire("change")}function e(t){var e;e=u(i,t),(n=l.toHsv()).h=360*(1-e.y),c(n,!0),a.fire("change")}i=a.getEl("h"),r=a.getEl("hp"),o=a.getEl("sv"),s=a.getEl("svp"),a._repaint=function(){c(n=l.toHsv())},a._super(),a._svdraghelper=new we(a._id+"-sv",{start:t,drag:t}),a._hdraghelper=new we(a._id+"-h",{start:e,drag:e}),a._repaint()},rgb:function(){return this.color().toRgb()},value:function(t){if(!arguments.length)return this.color().toHex();this.color().parse(t),this._rendered&&this._repaint()},color:function(){return this._color||(this._color=Un()),this._color},renderHtml:function(){var t,e=this._id,o=this.classPrefix,s="#ff0000,#ff0080,#ff00ff,#8000ff,#0000ff,#0080ff,#00ffff,#00ff80,#00ff00,#80ff00,#ffff00,#ff8000,#ff0000";return t='<div id="'+e+'-h" class="'+o+'colorpicker-h" style="background: -ms-linear-gradient(top,'+s+");background: linear-gradient(to bottom,"+s+');">'+function(){var t,e,n,i,r="";for(n="filter:progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr=",t=0,e=(i=s.split(",")).length-1;t<e;t++)r+='<div class="'+o+'colorpicker-h-chunk" style="height:'+100/e+"%;"+n+i[t]+",endColorstr="+i[t+1]+");-ms-"+n+i[t]+",endColorstr="+i[t+1]+')"></div>';return r}()+'<div id="'+e+'-hp" class="'+o+'colorpicker-h-marker"></div></div>','<div id="'+e+'" class="'+this.classes+'"><div id="'+e+'-sv" class="'+o+'colorpicker-sv"><div class="'+o+'colorpicker-overlay1"><div class="'+o+'colorpicker-overlay2"><div id="'+e+'-svp" class="'+o+'colorpicker-selector1"><div class="'+o+'colorpicker-selector2"></div></div></div></div></div>'+t+"</div>"}}),qn=ge.extend({init:function(t){t=R.extend({height:100,text:"Drop an image here",multiple:!1,accept:null},t),this._super(t),this.classes.add("dropzone"),t.multiple&&this.classes.add("multiple")},renderHtml:function(){var t,e,n=this.settings;return t={id:this._id,hidefocus:"1"},e=Ht.create("div",t,"<span>"+this.translate(n.text)+"</span>"),n.height&&Ht.css(e,"height",n.height+"px"),n.width&&Ht.css(e,"width",n.width+"px"),e.className=this.classes,e.outerHTML},postRender:function(){var i=this,t=function(t){t.preventDefault(),i.classes.toggle("dragenter"),i.getEl().className=i.classes};i._super(),i.$el.on("dragover",function(t){t.preventDefault()}),i.$el.on("dragenter",t),i.$el.on("dragleave",t),i.$el.on("drop",function(t){if(t.preventDefault(),!i.state.get("disabled")){var e=function(t){var e=i.settings.accept;if("string"!=typeof e)return t;var n=new RegExp("("+e.split(/\s*,\s*/).join("|")+")$","i");return R.grep(t,function(t){return n.test(t.name)})}(t.dataTransfer.files);i.value=function(){return e.length?i.settings.multiple?e:e[0]:null},e.length&&i.fire("change",t)}})},remove:function(){this.$el.off(),this._super()}}),Yn=ge.extend({init:function(t){var n=this;t.delimiter||(t.delimiter="\xbb"),n._super(t),n.classes.add("path"),n.canFocus=!0,n.on("click",function(t){var e;(e=t.target.getAttribute("data-index"))&&n.fire("select",{value:n.row()[e],index:e})}),n.row(n.settings.row)},focus:function(){return this.getEl().firstChild.focus(),this},row:function(t){return arguments.length?(this.state.set("row",t),this):this.state.get("row")},renderHtml:function(){return'<div id="'+this._id+'" class="'+this.classes+'">'+this._getDataPathHtml(this.state.get("row"))+"</div>"},bindStates:function(){var e=this;return e.state.on("change:row",function(t){e.innerHtml(e._getDataPathHtml(t.value))}),e._super()},_getDataPathHtml:function(t){var e,n,i=t||[],r="",o=this.classPrefix;for(e=0,n=i.length;e<n;e++)r+=(0<e?'<div class="'+o+'divider" aria-hidden="true"> '+this.settings.delimiter+" </div>":"")+'<div role="button" class="'+o+"path-item"+(e===n-1?" "+o+"last":"")+'" data-index="'+e+'" tabindex="-1" id="'+this._id+"-"+e+'" aria-level="'+(e+1)+'">'+i[e].name+"</div>";return r||(r='<div class="'+o+'path-item">\xa0</div>'),r}}),$n=Yn.extend({postRender:function(){var o=this,s=o.settings.editor;function a(t){if(1===t.nodeType){if("BR"===t.nodeName||t.getAttribute("data-mce-bogus"))return!0;if("bookmark"===t.getAttribute("data-mce-type"))return!0}return!1}return!1!==s.settings.elementpath&&(o.on("select",function(t){s.focus(),s.selection.select(this.row()[t.index].element),s.nodeChanged()}),s.on("nodeChange",function(t){for(var e=[],n=t.parents,i=n.length;i--;)if(1===n[i].nodeType&&!a(n[i])){var r=s.fire("ResolveName",{name:n[i].nodeName.toLowerCase(),target:n[i]});if(r.isDefaultPrevented()||e.push({name:r.name,element:n[i]}),r.isPropagationStopped())break}o.row(e)})),o._super()}}),Xn=Me.extend({Defaults:{layout:"flex",align:"center",defaults:{flex:1}},renderHtml:function(){var t=this,e=t._layout,n=t.classPrefix;return t.classes.add("formitem"),e.preRender(t),'<div id="'+t._id+'" class="'+t.classes+'" hidefocus="1" tabindex="-1">'+(t.settings.title?'<div id="'+t._id+'-title" class="'+n+'title">'+t.settings.title+"</div>":"")+'<div id="'+t._id+'-body" class="'+t.bodyClasses+'">'+(t.settings.html||"")+e.renderHtml(t)+"</div></div>"}}),jn=Me.extend({Defaults:{containerCls:"form",layout:"flex",direction:"column",align:"stretch",flex:1,padding:15,labelGap:30,spacing:10,callbacks:{submit:function(){this.submit()}}},preRender:function(){var i=this,t=i.items();i.settings.formItemDefaults||(i.settings.formItemDefaults={layout:"flex",autoResize:"overflow",defaults:{flex:1}}),t.each(function(t){var e,n=t.settings.label;n&&((e=new Xn(R.extend({items:{type:"label",id:t._id+"-l",text:n,flex:0,forId:t._id,disabled:t.disabled()}},i.settings.formItemDefaults))).type="formitem",t.aria("labelledby",t._id+"-l"),"undefined"==typeof t.settings.flex&&(t.settings.flex=1),i.replace(t,e),e.add(t))})},submit:function(){return this.fire("submit",{data:this.toJSON()})},postRender:function(){this._super(),this.fromJSON(this.settings.data)},bindStates:function(){var n=this;function t(){var t,e,i=0,r=[];if(!1!==n.settings.labelGapCalc)for(("children"===n.settings.labelGapCalc?n.find("formitem"):n.items()).filter("formitem").each(function(t){var e=t.items()[0],n=e.getEl().clientWidth;i=i<n?n:i,r.push(e)}),e=n.settings.labelGap||0,t=r.length;t--;)r[t].settings.minWidth=i+e}n._super(),n.on("show",t),t()}}),Jn=jn.extend({Defaults:{containerCls:"fieldset",layout:"flex",direction:"column",align:"stretch",flex:1,padding:"25 15 5 15",labelGap:30,spacing:10,border:1},renderHtml:function(){var t=this,e=t._layout,n=t.classPrefix;return t.preRender(),e.preRender(t),'<fieldset id="'+t._id+'" class="'+t.classes+'" hidefocus="1" tabindex="-1">'+(t.settings.title?'<legend id="'+t._id+'-title" class="'+n+'fieldset-title">'+t.settings.title+"</legend>":"")+'<div id="'+t._id+'-body" class="'+t.bodyClasses+'">'+(t.settings.html||"")+e.renderHtml(t)+"</div></fieldset>"}}),Gn=0,Kn=function(t){if(null===t||t===undefined)throw new Error("Node cannot be null or undefined");return{dom:lt(t)}},Zn={fromHtml:function(t,e){var n=(e||_.document).createElement("div");if(n.innerHTML=t,!n.hasChildNodes()||1<n.childNodes.length)throw _.console.error("HTML does not have a single root node",t),new Error("HTML must have a single root node");return Kn(n.childNodes[0])},fromTag:function(t,e){var n=(e||_.document).createElement(t);return Kn(n)},fromText:function(t,e){var n=(e||_.document).createTextNode(t);return Kn(n)},fromDom:Kn,fromPoint:function(t,e,n){var i=t.dom();return mt.from(i.elementFromPoint(e,n)).map(Kn)}},Qn=(_.Node.ATTRIBUTE_NODE,_.Node.CDATA_SECTION_NODE,_.Node.COMMENT_NODE,_.Node.DOCUMENT_NODE),ti=(_.Node.DOCUMENT_TYPE_NODE,_.Node.DOCUMENT_FRAGMENT_NODE,_.Node.ELEMENT_NODE),ei=(_.Node.TEXT_NODE,_.Node.PROCESSING_INSTRUCTION_NODE,_.Node.ENTITY_REFERENCE_NODE,_.Node.ENTITY_NODE,_.Node.NOTATION_NODE,function(t,e){var n=function(t,e){for(var n=0;n<t.length;n++){var i=t[n];if(i.test(e))return i}return undefined}(t,e);if(!n)return{major:0,minor:0};var i=function(t){return Number(e.replace(n,"$"+t))};return ii(i(1),i(2))}),ni=function(){return ii(0,0)},ii=function(t,e){return{major:t,minor:e}},ri={nu:ii,detect:function(t,e){var n=String(e).toLowerCase();return 0===t.length?ni():ei(t,n)},unknown:ni},oi="Firefox",si=function(t,e){return function(){return e===t}},ai=function(t){var e=t.current;return{current:e,version:t.version,isEdge:si("Edge",e),isChrome:si("Chrome",e),isIE:si("IE",e),isOpera:si("Opera",e),isFirefox:si(oi,e),isSafari:si("Safari",e)}},li={unknown:function(){return ai({current:undefined,version:ri.unknown()})},nu:ai,edge:lt("Edge"),chrome:lt("Chrome"),ie:lt("IE"),opera:lt("Opera"),firefox:lt(oi),safari:lt("Safari")},ui="Windows",ci="Android",di="Solaris",fi="FreeBSD",hi=function(t,e){return function(){return e===t}},mi=function(t){var e=t.current;return{current:e,version:t.version,isWindows:hi(ui,e),isiOS:hi("iOS",e),isAndroid:hi(ci,e),isOSX:hi("OSX",e),isLinux:hi("Linux",e),isSolaris:hi(di,e),isFreeBSD:hi(fi,e)}},gi={unknown:function(){return mi({current:undefined,version:ri.unknown()})},nu:mi,windows:lt(ui),ios:lt("iOS"),android:lt(ci),linux:lt("Linux"),osx:lt("OSX"),solaris:lt(di),freebsd:lt(fi)},pi=function(t,e){var n=String(e).toLowerCase();return function(t,e){for(var n=0,i=t.length;n<i;n++){var r=t[n];if(e(r,n))return mt.some(r)}return mt.none()}(t,function(t){return t.search(n)})},vi=function(t,n){return pi(t,n).map(function(t){var e=ri.detect(t.versionRegexes,n);return{current:t.name,version:e}})},bi=function(t,n){return pi(t,n).map(function(t){var e=ri.detect(t.versionRegexes,n);return{current:t.name,version:e}})},yi=function(t,e){return-1!==t.indexOf(e)},xi=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,wi=function(e){return function(t){return yi(t,e)}},_i=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(t){return yi(t,"edge/")&&yi(t,"chrome")&&yi(t,"safari")&&yi(t,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,xi],search:function(t){return yi(t,"chrome")&&!yi(t,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(t){return yi(t,"msie")||yi(t,"trident")}},{name:"Opera",versionRegexes:[xi,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:wi("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:wi("firefox")},{name:"Safari",versionRegexes:[xi,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(t){return(yi(t,"safari")||yi(t,"mobile/"))&&yi(t,"applewebkit")}}],Ri=[{name:"Windows",search:wi("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(t){return yi(t,"iphone")||yi(t,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:wi("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:wi("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:wi("linux"),versionRegexes:[]},{name:"Solaris",search:wi("sunos"),versionRegexes:[]},{name:"FreeBSD",search:wi("freebsd"),versionRegexes:[]}],Ci={browsers:lt(_i),oses:lt(Ri)},ki=function(t){var e,n,i,r,o,s,a,l,u,c,d,f=Ci.browsers(),h=Ci.oses(),m=vi(f,t).fold(li.unknown,li.nu),g=bi(h,t).fold(gi.unknown,gi.nu);return{browser:m,os:g,deviceType:(n=m,i=t,r=(e=g).isiOS()&&!0===/ipad/i.test(i),o=e.isiOS()&&!r,s=e.isAndroid()&&3===e.version.major,a=e.isAndroid()&&4===e.version.major,l=r||s||a&&!0===/mobile/i.test(i),u=e.isiOS()||e.isAndroid(),c=u&&!l,d=n.isSafari()&&e.isiOS()&&!1===/safari/i.test(i),{isiPad:lt(r),isiPhone:lt(o),isTablet:lt(l),isPhone:lt(c),isTouch:lt(u),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:lt(d)})}},Ei=(Je=!(Xe=function(){var t=_.navigator.userAgent;return ki(t)}),function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return Je||(Je=!0,je=Xe.apply(null,t)),je}),Hi=ti,Ti=Qn,Si=function(t){return t.nodeType!==Hi&&t.nodeType!==Ti||0===t.childElementCount},Mi=(Ei().browser.isIE(),function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e]}("element","offset"),R.trim),Ni=function(e){return function(t){if(t&&1===t.nodeType){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1}},Oi=Ni("true"),Wi=Ni("false"),Pi=function(t,e,n,i,r){return{type:t,title:e,url:n,level:i,attach:r}},Di=function(t){return t.innerText||t.textContent},Ai=function(t){return t.id?t.id:(e="h",n=(new Date).getTime(),e+"_"+Math.floor(1e9*Math.random())+ ++Gn+String(n));var e,n},Bi=function(t){return(e=t)&&"A"===e.nodeName&&(e.id||e.name)&&Ii(t);var e},Li=function(t){return t&&/^(H[1-6])$/.test(t.nodeName)},Ii=function(t){return function(t){for(;t=t.parentNode;){var e=t.contentEditable;if(e&&"inherit"!==e)return Oi(t)}return!1}(t)&&!Wi(t)},zi=function(t){return Li(t)&&Ii(t)},Fi=function(t){var e,n=Ai(t);return Pi("header",Di(t),"#"+n,Li(e=t)?parseInt(e.nodeName.substr(1),10):0,function(){t.id=n})},Ui=function(t){var e=t.id||t.name,n=Di(t);return Pi("anchor",n||"#"+e,"#"+e,0,at)},Vi=function(t){var e,n,i,r,o,s;return e="h1,h2,h3,h4,h5,h6,a:not([href])",n=t,Rt((Ei().browser.isIE(),function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e]}("element","offset"),i=Zn.fromDom(n),r=e,s=(o=i)===undefined?_.document:o.dom(),Si(s)?[]:Rt(s.querySelectorAll(r),Zn.fromDom)),function(t){return t.dom()})},qi=function(t){return 0<Mi(t.title).length},Yi=function(t){var e,n=Vi(t);return kt((e=n,Rt(kt(e,zi),Fi)).concat(Rt(kt(n,Bi),Ui)),qi)},$i={},Xi=function(t){return{title:t.title,value:{title:{raw:t.title},url:t.url,attach:t.attach}}},ji=function(t,e){return{title:t,value:{title:t,url:e,attach:at}}},Ji=function(t,e,n){var i=e in t?t[e]:n;return!1===i?null:i},Gi=function(t,i,r,e){var n,o,s,a,l,u,c={title:"-"},d=function(t){var e=t.hasOwnProperty(r)?t[r]:[],n=kt(e,function(t){return e=t,!_t(i,function(t){return t.url===e});var e});return R.map(n,function(t){return{title:t,value:{title:t,url:t,attach:at}}})},f=function(e){var t,n=kt(i,function(t){return t.type===e});return t=n,R.map(t,Xi)};return!1===e.typeahead_urls?[]:"file"===r?(n=[Ki(t,d($i)),Ki(t,f("header")),Ki(t,(a=f("anchor"),l=Ji(e,"anchor_top","#top"),u=Ji(e,"anchor_bottom","#bottom"),null!==l&&a.unshift(ji("<top>",l)),null!==u&&a.push(ji("<bottom>",u)),a))],o=function(t,e){return 0===t.length||0===e.length?t.concat(e):t.concat(c,e)},s=[],Ct(n,function(t){s=o(s,t)}),s):Ki(t,d($i))},Ki=function(t,e){var n=t.toLowerCase(),i=R.grep(e,function(t){return-1!==t.title.toLowerCase().indexOf(n)});return 1===i.length&&i[0].title===t?[]:i},Zi=function(r,i,o,s){var e=function(t){var e=Yi(o),n=Gi(t,e,s,i);r.showAutoComplete(n,t)};r.on("autocomplete",function(){e(r.value())}),r.on("selectitem",function(t){var e=t.value;r.value(e.url);var n,i=(n=e.title).raw?n.raw:n;"image"===s?r.fire("change",{meta:{alt:i,attach:e.attach}}):r.fire("change",{meta:{text:i,attach:e.attach}}),r.focus()}),r.on("click",function(t){0===r.value().length&&"INPUT"===t.target.nodeName&&e("")}),r.on("PostRender",function(){r.getRoot().on("submit",function(t){var e,n,i;t.isDefaultPrevented()||(e=r.value(),i=$i[n=s],/^https?/.test(e)&&(i?wt(i,e).isNone()&&($i[n]=i.slice(0,5).concat(e)):$i[n]=[e]))})})},Qi=function(o,t,n){var i=t.filepicker_validator_handler;i&&o.state.on("change:value",function(t){var e;0!==(e=t.value).length?i({url:e,type:n},function(t){var e,n,i,r=(n=(e=t).status,i=e.message,"valid"===n?{status:"ok",message:i}:"unknown"===n?{status:"warn",message:i}:"invalid"===n?{status:"warn",message:i}:{status:"none",message:""});o.statusMessage(r.message),o.statusLevel(r.status)}):o.statusLevel("none")})},tr=Bn.extend({Statics:{clearHistory:function(){$i={}}},init:function(t){var e,n,i,r=this,o=window.tinymce?window.tinymce.activeEditor:M.activeEditor,s=o.settings,a=t.filetype;t.spellcheck=!1,(i=s.file_picker_types||s.file_browser_callback_types)&&(i=R.makeMap(i,/[, ]/)),i&&!i[a]||(!(n=s.file_picker_callback)||i&&!i[a]?!(n=s.file_browser_callback)||i&&!i[a]||(e=function(){n(r.getEl("inp").id,r.value(),a,window)}):e=function(){var t=r.fire("beforecall").meta;t=R.extend({filetype:a},t),n.call(o,function(t,e){r.value(t).fire("change",{meta:e})},r.value(),t)}),e&&(t.icon="browse",t.onaction=e),r._super(t),r.classes.add("filepicker"),Zi(r,s,o.getBody(),a),Qi(r,s,a)}}),er=Nn.extend({recalc:function(t){var e=t.layoutRect(),n=t.paddingBox;t.items().filter(":visible").each(function(t){t.layoutRect({x:n.left,y:n.top,w:e.innerW-n.right-n.left,h:e.innerH-n.top-n.bottom}),t.recalc&&t.recalc()})}}),nr=Nn.extend({recalc:function(t){var e,n,i,r,o,s,a,l,u,c,d,f,h,m,g,p,v,b,y,x,w,_,R,C,k,E,H,T,S,M,N,O,W,P,D,A,B,L=[],I=Math.max,z=Math.min;for(i=t.items().filter(":visible"),r=t.layoutRect(),o=t.paddingBox,s=t.settings,f=t.isRtl()?s.direction||"row-reversed":s.direction,a=s.align,l=t.isRtl()?s.pack||"end":s.pack,u=s.spacing||0,"row-reversed"!==f&&"column-reverse"!==f||(i=i.set(i.toArray().reverse()),f=f.split("-")[0]),"column"===f?(C="y",_="h",R="minH",k="maxH",H="innerH",E="top",T="deltaH",S="contentH",P="left",O="w",M="x",N="innerW",W="minW",D="right",A="deltaW",B="contentW"):(C="x",_="w",R="minW",k="maxW",H="innerW",E="left",T="deltaW",S="contentW",P="top",O="h",M="y",N="innerH",W="minH",D="bottom",A="deltaH",B="contentH"),d=r[H]-o[E]-o[E],w=c=0,e=0,n=i.length;e<n;e++)m=(h=i[e]).layoutRect(),d-=e<n-1?u:0,0<(g=h.settings.flex)&&(c+=g,m[k]&&L.push(h),m.flex=g),d-=m[R],w<(p=o[P]+m[W]+o[D])&&(w=p);if((y={})[R]=d<0?r[R]-d+r[T]:r[H]-d+r[T],y[W]=w+r[A],y[S]=r[H]-d,y[B]=w,y.minW=z(y.minW,r.maxW),y.minH=z(y.minH,r.maxH),y.minW=I(y.minW,r.startMinWidth),y.minH=I(y.minH,r.startMinHeight),!r.autoResize||y.minW===r.minW&&y.minH===r.minH){for(b=d/c,e=0,n=L.length;e<n;e++)(v=(m=(h=L[e]).layoutRect())[k])<(p=m[R]+m.flex*b)?(d-=m[k]-m[R],c-=m.flex,m.flex=0,m.maxFlexSize=v):m.maxFlexSize=0;for(b=d/c,x=o[E],y={},0===c&&("end"===l?x=d+o[E]:"center"===l?(x=Math.round(r[H]/2-(r[H]-d)/2)+o[E])<0&&(x=o[E]):"justify"===l&&(x=o[E],u=Math.floor(d/(i.length-1)))),y[M]=o[P],e=0,n=i.length;e<n;e++)p=(m=(h=i[e]).layoutRect()).maxFlexSize||m[R],"center"===a?y[M]=Math.round(r[N]/2-m[O]/2):"stretch"===a?(y[O]=I(m[W]||0,r[N]-o[P]-o[D]),y[M]=o[P]):"end"===a&&(y[M]=r[N]-m[O]-o.top),0<m.flex&&(p+=m.flex*b),y[_]=p,y[C]=x,h.layoutRect(y),h.recalc&&h.recalc(),x+=p+u}else if(y.w=y.minW,y.h=y.minH,t.layoutRect(y),this.recalc(t),null===t._lastRect){var F=t.parent();F&&(F._lastRect=null,F.recalc())}}}),ir=Mn.extend({Defaults:{containerClass:"flow-layout",controlClass:"flow-layout-item",endClass:"break"},recalc:function(t){t.items().filter(":visible").each(function(t){t.recalc&&t.recalc()})},isNative:function(){return!0}}),rr=function(t,e){return n=e,r=(i=t)===undefined?_.document:i.dom(),Si(r)?mt.none():mt.from(r.querySelector(n)).map(Zn.fromDom);var n,i,r},or=function(t,e){return function(){t.execCommand("mceToggleFormat",!1,e)}},sr=function(t,e,n){var i=function(t){n(t,e)};t.formatter?t.formatter.formatChanged(e,i):t.on("init",function(){t.formatter.formatChanged(e,i)})},ar=function(t,n){return function(e){sr(t,n,function(t){e.control.active(t)})}},lr=function(i){var e=["alignleft","aligncenter","alignright","alignjustify"],r="alignleft",t=[{text:"Left",icon:"alignleft",onclick:or(i,"alignleft")},{text:"Center",icon:"aligncenter",onclick:or(i,"aligncenter")},{text:"Right",icon:"alignright",onclick:or(i,"alignright")},{text:"Justify",icon:"alignjustify",onclick:or(i,"alignjustify")}];i.addMenuItem("align",{text:"Align",menu:t}),i.addButton("align",{type:"menubutton",icon:r,menu:t,onShowMenu:function(t){var n=t.control.menu;R.each(e,function(e,t){n.items().eq(t).each(function(t){return t.active(i.formatter.match(e))})})},onPostRender:function(t){var n=t.control;R.each(e,function(e,t){sr(i,e,function(t){n.icon(r),t&&n.icon(e)})})}}),R.each({alignleft:["Align left","JustifyLeft"],aligncenter:["Align center","JustifyCenter"],alignright:["Align right","JustifyRight"],alignjustify:["Justify","JustifyFull"],alignnone:["No alignment","JustifyNone"]},function(t,e){i.addButton(e,{active:!1,tooltip:t[0],cmd:t[1],onPostRender:ar(i,e)})})},ur=function(t){return t?t.split(",")[0]:""},cr=function(l,u){return function(){var a=this;a.state.set("value",null),l.on("init nodeChange",function(t){var e,n,i,r,o=l.queryCommandValue("FontName"),s=(e=u,r=(n=o)?n.toLowerCase():"",R.each(e,function(t){t.value.toLowerCase()===r&&(i=t.value)}),R.each(e,function(t){i||ur(t.value).toLowerCase()!==ur(r).toLowerCase()||(i=t.value)}),i);a.value(s||null),!s&&o&&a.text(ur(o))})}},dr=function(n){n.addButton("fontselect",function(){var t,e=(t=function(t){for(var e=(t=t.replace(/;$/,"").split(";")).length;e--;)t[e]=t[e].split("=");return t}(n.settings.font_formats||"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"),R.map(t,function(t){return{text:{raw:t[0]},value:t[1],textStyle:-1===t[1].indexOf("dings")?"font-family:"+t[1]:""}}));return{type:"listbox",text:"Font Family",tooltip:"Font Family",values:e,fixedWidth:!0,onPostRender:cr(n,e),onselect:function(t){t.control.settings.value&&n.execCommand("FontName",!1,t.control.settings.value)}}})},fr=function(t){dr(t)},hr=function(t,e){return/[0-9.]+px$/.test(t)?(n=72*parseInt(t,10)/96,i=e||0,r=Math.pow(10,i),Math.round(n*r)/r+"pt"):t;var n,i,r},mr=function(t,e,n){var i;return R.each(t,function(t){t.value===n?i=n:t.value===e&&(i=e)}),i},gr=function(n){n.addButton("fontsizeselect",function(){var t,s,a,e=(t=n.settings.fontsize_formats||"8pt 10pt 12pt 14pt 18pt 24pt 36pt",R.map(t.split(" "),function(t){var e=t,n=t,i=t.split("=");return 1<i.length&&(e=i[0],n=i[1]),{text:e,value:n}}));return{type:"listbox",text:"Font Sizes",tooltip:"Font Sizes",values:e,fixedWidth:!0,onPostRender:(s=n,a=e,function(){var o=this;s.on("init nodeChange",function(t){var e,n,i,r;if(e=s.queryCommandValue("FontSize"))for(i=3;!r&&0<=i;i--)n=hr(e,i),r=mr(a,n,e);o.value(r||null),r||o.text(n)})}),onclick:function(t){t.control.settings.value&&n.execCommand("FontSize",!1,t.control.settings.value)}}})},pr=function(t){gr(t)},vr=function(n,t){var i=t.length;return R.each(t,function(t){t.menu&&(t.hidden=0===vr(n,t.menu));var e=t.format;e&&(t.hidden=!n.formatter.canApply(e)),t.hidden&&i--}),i},br=function(n,t){var i=t.items().length;return t.items().each(function(t){t.menu&&t.visible(0<br(n,t.menu)),!t.menu&&t.settings.menu&&t.visible(0<vr(n,t.settings.menu));var e=t.settings.format;e&&t.visible(n.formatter.canApply(e)),t.visible()||i--}),i},yr=function(t){var i,r,o,e,s,n,a,l,u=(r=0,o=[],e=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],s=function(t){var i=[];if(t)return R.each(t,function(t){var e={text:t.title,icon:t.icon};if(t.items)e.menu=s(t.items);else{var n=t.format||"custom"+r++;t.format||(t.name=n,o.push(t)),e.format=n,e.cmd=t.cmd}i.push(e)}),i},(i=t).on("init",function(){R.each(o,function(t){i.formatter.register(t.name,t)})}),{type:"menu",items:i.settings.style_formats_merge?i.settings.style_formats?s(e.concat(i.settings.style_formats)):s(e):s(i.settings.style_formats||e),onPostRender:function(t){i.fire("renderFormatsMenu",{control:t.control})},itemDefaults:{preview:!0,textStyle:function(){if(this.settings.format)return i.formatter.getCssText(this.settings.format)},onPostRender:function(){var n=this;n.parent().on("show",function(){var t,e;(t=n.settings.format)&&(n.disabled(!i.formatter.canApply(t)),n.active(i.formatter.match(t))),(e=n.settings.cmd)&&n.active(i.queryCommandState(e))})},onclick:function(){this.settings.format&&or(i,this.settings.format)(),this.settings.cmd&&i.execCommand(this.settings.cmd)}}});n=u,t.addMenuItem("formats",{text:"Formats",menu:n}),l=u,(a=t).addButton("styleselect",{type:"menubutton",text:"Formats",menu:l,onShowMenu:function(){a.settings.style_formats_autohide&&br(a,this.menu)}})},xr=function(n,t){return function(){var r,o,s,e=[];return R.each(t,function(t){e.push({text:t[0],value:t[1],textStyle:function(){return n.formatter.getCssText(t[1])}})}),{type:"listbox",text:t[0][0],values:e,fixedWidth:!0,onselect:function(t){if(t.control){var e=t.control.value();or(n,e)()}},onPostRender:(r=n,o=e,function(){var e=this;r.on("nodeChange",function(t){var n=r.formatter,i=null;R.each(t.parents,function(e){if(R.each(o,function(t){if(s?n.matchNode(e,s,{value:t.value})&&(i=t.value):n.matchNode(e,t.value)&&(i=t.value),i)return!1}),i)return!1}),e.value(i)})})}}},wr=function(t){var e,n,i=function(t){for(var e=(t=t.replace(/;$/,"").split(";")).length;e--;)t[e]=t[e].split("=");return t}(t.settings.block_formats||"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre");t.addMenuItem("blockformats",{text:"Blocks",menu:(e=t,n=i,R.map(n,function(t){return{text:t[0],onclick:or(e,t[1]),textStyle:function(){return e.formatter.getCssText(t[1])}}}))}),t.addButton("formatselect",xr(t,i))},_r=function(e,t){var n,i;if("string"==typeof t)i=t.split(" ");else if(R.isArray(t))return function(t){for(var e=[],n=0,i=t.length;n<i;++n){if(!pt(t[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+t);xt.apply(e,t[n])}return e}(R.map(t,function(t){return _r(e,t)}));return n=R.grep(i,function(t){return"|"===t||t in e.menuItems}),R.map(n,function(t){return"|"===t?{text:"-"}:e.menuItems[t]})},Rr=function(t){return t&&"-"===t.text},Cr=function(n){var i=kt(n,function(t,e){return!Rr(t)||!Rr(n[e-1])});return kt(i,function(t,e){return!Rr(t)||0<e&&e<i.length-1})},kr=function(t){var e,n,i,r,o=t.settings.insert_button_items;return Cr(o?_r(t,o):(e=t,n="insert",i=[{text:"-"}],r=R.grep(e.menuItems,function(t){return t.context===n}),R.each(r,function(t){"before"===t.separator&&i.push({text:"|"}),t.prependToContext?i.unshift(t):i.push(t),"after"===t.separator&&i.push({text:"|"})}),i))},Er=function(t){var e;(e=t).addButton("insert",{type:"menubutton",icon:"insert",menu:[],oncreatemenu:function(){this.menu.add(kr(e)),this.menu.renderNew()}})},Hr=function(t){var n,i,r;n=t,R.each({bold:"Bold",italic:"Italic",underline:"Underline",strikethrough:"Strikethrough",subscript:"Subscript",superscript:"Superscript"},function(t,e){n.addButton(e,{active:!1,tooltip:t,onPostRender:ar(n,e),onclick:or(n,e)})}),i=t,R.each({outdent:["Decrease indent","Outdent"],indent:["Increase indent","Indent"],cut:["Cut","Cut"],copy:["Copy","Copy"],paste:["Paste","Paste"],help:["Help","mceHelp"],selectall:["Select all","SelectAll"],visualaid:["Visual aids","mceToggleVisualAid"],newdocument:["New document","mceNewDocument"],removeformat:["Clear formatting","RemoveFormat"],remove:["Remove","Delete"]},function(t,e){i.addButton(e,{tooltip:t[0],cmd:t[1]})}),r=t,R.each({blockquote:["Blockquote","mceBlockQuote"],subscript:["Subscript","Subscript"],superscript:["Superscript","Superscript"]},function(t,e){r.addButton(e,{active:!1,tooltip:t[0],cmd:t[1],onPostRender:ar(r,e)})})},Tr=function(t){var n;Hr(t),n=t,R.each({bold:["Bold","Bold","Meta+B"],italic:["Italic","Italic","Meta+I"],underline:["Underline","Underline","Meta+U"],strikethrough:["Strikethrough","Strikethrough"],subscript:["Subscript","Subscript"],superscript:["Superscript","Superscript"],removeformat:["Clear formatting","RemoveFormat"],newdocument:["New document","mceNewDocument"],cut:["Cut","Cut","Meta+X"],copy:["Copy","Copy","Meta+C"],paste:["Paste","Paste","Meta+V"],selectall:["Select all","SelectAll","Meta+A"]},function(t,e){n.addMenuItem(e,{text:t[0],icon:e,shortcut:t[2],cmd:t[1]})}),n.addMenuItem("codeformat",{text:"Code",icon:"code",onclick:or(n,"code")})},Sr=function(n,i){return function(){var t=this,e=function(){var t="redo"===i?"hasRedo":"hasUndo";return!!n.undoManager&&n.undoManager[t]()};t.disabled(!e()),n.on("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",function(){t.disabled(n.readonly||!e())})}},Mr=function(t){var e,n;(e=t).addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onPostRender:Sr(e,"undo"),cmd:"undo"}),e.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onPostRender:Sr(e,"redo"),cmd:"redo"}),(n=t).addButton("undo",{tooltip:"Undo",onPostRender:Sr(n,"undo"),cmd:"undo"}),n.addButton("redo",{tooltip:"Redo",onPostRender:Sr(n,"redo"),cmd:"redo"})},Nr=function(t){var e,n;(e=t).addMenuItem("visualaid",{text:"Visual aids",selectable:!0,onPostRender:(n=e,function(){var e=this;n.on("VisualAid",function(t){e.active(t.hasVisual)}),e.active(n.hasVisual)}),cmd:"mceToggleVisualAid"})},Or={setup:function(t){var e;t.rtl&&(ae.rtl=!0),t.on("mousedown progressstate",function(){ze.hideAll()}),(e=t).settings.ui_container&&(h.container=rr(Zn.fromDom(_.document.body),e.settings.ui_container).fold(lt(null),function(t){return t.dom()})),ge.tooltips=!h.iOS,ae.translate=function(t){return M.translate(t)},wr(t),lr(t),Tr(t),Mr(t),pr(t),fr(t),yr(t),Nr(t),Er(t)}},Wr=Nn.extend({recalc:function(t){var e,n,i,r,o,s,a,l,u,c,d,f,h,m,g,p,v,b,y,x,w,_,R,C,k,E,H,T,S=[],M=[];e=t.settings,r=t.items().filter(":visible"),o=t.layoutRect(),i=e.columns||Math.ceil(Math.sqrt(r.length)),n=Math.ceil(r.length/i),b=e.spacingH||e.spacing||0,y=e.spacingV||e.spacing||0,x=e.alignH||e.align,w=e.alignV||e.align,p=t.paddingBox,T="reverseRows"in e?e.reverseRows:t.isRtl(),x&&"string"==typeof x&&(x=[x]),w&&"string"==typeof w&&(w=[w]);for(d=0;d<i;d++)S.push(0);for(f=0;f<n;f++)M.push(0);for(f=0;f<n;f++)for(d=0;d<i&&(c=r[f*i+d]);d++)C=(u=c.layoutRect()).minW,k=u.minH,S[d]=C>S[d]?C:S[d],M[f]=k>M[f]?k:M[f];for(E=o.innerW-p.left-p.right,d=_=0;d<i;d++)_+=S[d]+(0<d?b:0),E-=(0<d?b:0)+S[d];for(H=o.innerH-p.top-p.bottom,f=R=0;f<n;f++)R+=M[f]+(0<f?y:0),H-=(0<f?y:0)+M[f];if(_+=p.left+p.right,R+=p.top+p.bottom,(l={}).minW=_+(o.w-o.innerW),l.minH=R+(o.h-o.innerH),l.contentW=l.minW-o.deltaW,l.contentH=l.minH-o.deltaH,l.minW=Math.min(l.minW,o.maxW),l.minH=Math.min(l.minH,o.maxH),l.minW=Math.max(l.minW,o.startMinWidth),l.minH=Math.max(l.minH,o.startMinHeight),!o.autoResize||l.minW===o.minW&&l.minH===o.minH){var N;o.autoResize&&((l=t.layoutRect(l)).contentW=l.minW-o.deltaW,l.contentH=l.minH-o.deltaH),N="start"===e.packV?0:0<H?Math.floor(H/n):0;var O=0,W=e.flexWidths;if(W)for(d=0;d<W.length;d++)O+=W[d];else O=i;var P=E/O;for(d=0;d<i;d++)S[d]+=W?W[d]*P:P;for(m=p.top,f=0;f<n;f++){for(h=p.left,a=M[f]+N,d=0;d<i&&(c=r[T?f*i+i-1-d:f*i+d]);d++)g=c.settings,u=c.layoutRect(),s=Math.max(S[d],u.startMinWidth),u.x=h,u.y=m,"center"===(v=g.alignH||(x?x[d]||x[0]:null))?u.x=h+s/2-u.w/2:"right"===v?u.x=h+s-u.w:"stretch"===v&&(u.w=s),"center"===(v=g.alignV||(w?w[d]||w[0]:null))?u.y=m+a/2-u.h/2:"bottom"===v?u.y=m+a-u.h:"stretch"===v&&(u.h=a),c.layoutRect(u),h+=s+b,c.recalc&&c.recalc();m+=a+y}}else if(l.w=l.minW,l.h=l.minH,t.layoutRect(l),this.recalc(t),null===t._lastRect){var D=t.parent();D&&(D._lastRect=null,D.recalc())}}}),Pr=ge.extend({renderHtml:function(){var t=this;return t.classes.add("iframe"),t.canFocus=!1,'<iframe id="'+t._id+'" class="'+t.classes+'" tabindex="-1" src="'+(t.settings.url||"javascript:''")+'" frameborder="0"></iframe>'},src:function(t){this.getEl().src=t},html:function(t,e){var n=this,i=this.getEl().contentWindow.document.body;return i?(i.innerHTML=t,e&&e()):c.setTimeout(function(){n.html(t)}),this}}),Dr=ge.extend({init:function(t){this._super(t),this.classes.add("widget").add("infobox"),this.canFocus=!1},severity:function(t){this.classes.remove("error"),this.classes.remove("warning"),this.classes.remove("success"),this.classes.add(t)},help:function(t){this.state.set("help",t)},renderHtml:function(){var t=this,e=t.classPrefix;return'<div id="'+t._id+'" class="'+t.classes+'"><div id="'+t._id+'-body">'+t.encode(t.state.get("text"))+'<button role="button" tabindex="-1"><i class="'+e+"ico "+e+'i-help"></i></button></div></div>'},bindStates:function(){var e=this;return e.state.on("change:text",function(t){e.getEl("body").firstChild.data=e.encode(t.value),e.state.get("rendered")&&e.updateLayoutRect()}),e.state.on("change:help",function(t){e.classes.toggle("has-help",t.value),e.state.get("rendered")&&e.updateLayoutRect()}),e._super()}}),Ar=ge.extend({init:function(t){var e=this;e._super(t),e.classes.add("widget").add("label"),e.canFocus=!1,t.multiline&&e.classes.add("autoscroll"),t.strong&&e.classes.add("strong")},initLayoutRect:function(){var t=this,e=t._super();return t.settings.multiline&&(Ht.getSize(t.getEl()).width>e.maxW&&(e.minW=e.maxW,t.classes.add("multiline")),t.getEl().style.width=e.minW+"px",e.startMinH=e.h=e.minH=Math.min(e.maxH,Ht.getSize(t.getEl()).height)),e},repaint:function(){return this.settings.multiline||(this.getEl().style.lineHeight=this.layoutRect().h+"px"),this._super()},severity:function(t){this.classes.remove("error"),this.classes.remove("warning"),this.classes.remove("success"),this.classes.add(t)},renderHtml:function(){var t,e,n=this,i=n.settings.forId,r=n.settings.html?n.settings.html:n.encode(n.state.get("text"));return!i&&(e=n.settings.forName)&&(t=n.getRoot().find("#"+e)[0])&&(i=t._id),i?'<label id="'+n._id+'" class="'+n.classes+'"'+(i?' for="'+i+'"':"")+">"+r+"</label>":'<span id="'+n._id+'" class="'+n.classes+'">'+r+"</span>"},bindStates:function(){var e=this;return e.state.on("change:text",function(t){e.innerHtml(e.encode(t.value)),e.state.get("rendered")&&e.updateLayoutRect()}),e._super()}}),Br=Me.extend({Defaults:{role:"toolbar",layout:"flow"},init:function(t){this._super(t),this.classes.add("toolbar")},postRender:function(){return this.items().each(function(t){t.classes.add("toolbar-item")}),this._super()}}),Lr=Br.extend({Defaults:{role:"menubar",containerCls:"menubar",ariaRoot:!0,defaults:{type:"menubutton"}}}),Ir=On.extend({init:function(t){var e=this;e._renderOpen=!0,e._super(t),t=e.settings,e.classes.add("menubtn"),t.fixedWidth&&e.classes.add("fixed-width"),e.aria("haspopup",!0),e.state.set("menu",t.menu||e.render())},showMenu:function(t){var e,n=this;if(n.menu&&n.menu.visible()&&!1!==t)return n.hideMenu();n.menu||(e=n.state.get("menu")||[],n.classes.add("opened"),e.length?e={type:"menu",animate:!0,items:e}:(e.type=e.type||"menu",e.animate=!0),e.renderTo?n.menu=e.parent(n).show().renderTo():n.menu=_e.create(e).parent(n).renderTo(),n.fire("createmenu"),n.menu.reflow(),n.menu.on("cancel",function(t){t.control.parent()===n.menu&&(t.stopPropagation(),n.focus(),n.hideMenu())}),n.menu.on("select",function(){n.focus()}),n.menu.on("show hide",function(t){"hide"===t.type&&t.control.parent()===n&&n.classes.remove("opened-under"),t.control===n.menu&&(n.activeMenu("show"===t.type),n.classes.toggle("opened","show"===t.type)),n.aria("expanded","show"===t.type)}).fire("show")),n.menu.show(),n.menu.layoutRect({w:n.layoutRect().w}),n.menu.repaint(),n.menu.moveRel(n.getEl(),n.isRtl()?["br-tr","tr-br"]:["bl-tl","tl-bl"]);var i=n.menu.layoutRect(),r=n.$el.offset().top+n.layoutRect().h;r>i.y&&r<i.y+i.h&&n.classes.add("opened-under"),n.fire("showmenu")},hideMenu:function(){this.menu&&(this.menu.items().each(function(t){t.hideMenu&&t.hideMenu()}),this.menu.hide())},activeMenu:function(t){this.classes.toggle("active",t)},renderHtml:function(){var t,e=this,n=e._id,i=e.classPrefix,r=e.settings.icon,o=e.state.get("text"),s="";return(t=e.settings.image)?(r="none","string"!=typeof t&&(t=_.window.getSelection?t[0]:t[1]),t=" style=\"background-image: url('"+t+"')\""):t="",o&&(e.classes.add("btn-has-text"),s='<span class="'+i+'txt">'+e.encode(o)+"</span>"),r=e.settings.icon?i+"ico "+i+"i-"+r:"",e.aria("role",e.parent()instanceof Lr?"menuitem":"button"),'<div id="'+n+'" class="'+e.classes+'" tabindex="-1" aria-labelledby="'+n+'"><button id="'+n+'-open" role="presentation" type="button" tabindex="-1">'+(r?'<i class="'+r+'"'+t+"></i>":"")+s+' <i class="'+i+'caret"></i></button></div>'},postRender:function(){var r=this;return r.on("click",function(t){t.control===r&&function(t,e){for(;t;){if(e===t)return!0;t=t.parentNode}return!1}(t.target,r.getEl())&&(r.focus(),r.showMenu(!t.aria),t.aria&&r.menu.items().filter(":visible")[0].focus())}),r.on("mouseenter",function(t){var e,n=t.control,i=r.parent();n&&i&&n instanceof Ir&&n.parent()===i&&(i.items().filter("MenuButton").each(function(t){t.hideMenu&&t!==n&&(t.menu&&t.menu.visible()&&(e=!0),t.hideMenu())}),e&&(n.focus(),n.showMenu()))}),r._super()},bindStates:function(){var t=this;return t.state.on("change:menu",function(){t.menu&&t.menu.remove(),t.menu=null}),t._super()},remove:function(){this._super(),this.menu&&this.menu.remove()}});function zr(i,r){var o,s,a=this,l=ae.classPrefix;a.show=function(t,e){function n(){o&&(Tt(i).append('<div class="'+l+"throbber"+(r?" "+l+"throbber-inline":"")+'"></div>'),e&&e())}return a.hide(),o=!0,t?s=c.setTimeout(n,t):n(),a},a.hide=function(){var t=i.lastChild;return c.clearTimeout(s),t&&-1!==t.className.indexOf("throbber")&&t.parentNode.removeChild(t),o=!1,a}}var Fr=ze.extend({Defaults:{defaultType:"menuitem",border:1,layout:"stack",role:"application",bodyRole:"menu",ariaRoot:!0},init:function(t){if(t.autohide=!0,t.constrainToViewport=!0,"function"==typeof t.items&&(t.itemsFactory=t.items,t.items=[]),t.itemDefaults)for(var e=t.items,n=e.length;n--;)e[n]=R.extend({},t.itemDefaults,e[n]);this._super(t),this.classes.add("menu"),t.animate&&11!==h.ie&&this.classes.add("animate")},repaint:function(){return this.classes.toggle("menu-align",!0),this._super(),this.getEl().style.height="",this.getEl("body").style.height="",this},cancel:function(){this.hideAll(),this.fire("select")},load:function(){var e,n=this;function i(){n.throbber&&(n.throbber.hide(),n.throbber=null)}n.settings.itemsFactory&&(n.throbber||(n.throbber=new zr(n.getEl("body"),!0),0===n.items().length?(n.throbber.show(),n.fire("loading")):n.throbber.show(100,function(){n.items().remove(),n.fire("loading")}),n.on("hide close",i)),n.requestTime=e=(new Date).getTime(),n.settings.itemsFactory(function(t){0!==t.length?n.requestTime===e&&(n.getEl().style.width="",n.getEl("body").style.width="",i(),n.items().remove(),n.getEl("body").innerHTML="",n.add(t),n.renderNew(),n.fire("loaded")):n.hide()}))},hideAll:function(){return this.find("menuitem").exec("hideMenu"),this._super()},preRender:function(){var n=this;return n.items().each(function(t){var e=t.settings;if(e.icon||e.image||e.selectable)return!(n._hasIcons=!0)}),n.settings.itemsFactory&&n.on("postrender",function(){n.settings.itemsFactory&&n.load()}),n.on("show hide",function(t){t.control===n&&("show"===t.type?c.setTimeout(function(){n.classes.add("in")},0):n.classes.remove("in"))}),n._super()}}),Ur=Ir.extend({init:function(i){var e,r,o,n,s=this;s._super(i),i=s.settings,s._values=e=i.values,e&&("undefined"!=typeof i.value&&function t(e){for(var n=0;n<e.length;n++){if(r=e[n].selected||i.value===e[n].value)return o=o||e[n].text,s.state.set("value",e[n].value),!0;if(e[n].menu&&t(e[n].menu))return!0}}(e),!r&&0<e.length&&(o=e[0].text,s.state.set("value",e[0].value)),s.state.set("menu",e)),s.state.set("text",i.text||o),s.classes.add("listbox"),s.on("select",function(t){var e=t.control;n&&(t.lastControl=n),i.multiple?e.active(!e.active()):s.value(t.control.value()),n=e})},value:function(n){return 0===arguments.length?this.state.get("value"):(void 0===n||(this.settings.values&&!function e(t){return _t(t,function(t){return t.menu?e(t.menu):t.value===n})}(this.settings.values)?null===n&&this.state.set("value",null):this.state.set("value",n)),this)},bindStates:function(){var i=this;return i.on("show",function(t){var e,n;e=t.control,n=i.value(),e instanceof Fr&&e.items().each(function(t){t.hasMenus()||t.active(t.value()===n)})}),i.state.on("change:value",function(e){var n=function t(e,n){var i;if(e)for(var r=0;r<e.length;r++){if(e[r].value===n)return e[r];if(e[r].menu&&(i=t(e[r].menu,n)))return i}}(i.state.get("menu"),e.value);n?i.text(n.text):i.text(i.settings.text)}),i._super()}}),Vr=ge.extend({Defaults:{border:0,role:"menuitem"},init:function(t){var e,n=this;n._super(t),t=n.settings,n.classes.add("menu-item"),t.menu&&n.classes.add("menu-item-expand"),t.preview&&n.classes.add("menu-item-preview"),"-"!==(e=n.state.get("text"))&&"|"!==e||(n.classes.add("menu-item-sep"),n.aria("role","separator"),n.state.set("text","-")),t.selectable&&(n.aria("role","menuitemcheckbox"),n.classes.add("menu-item-checkbox"),t.icon="selected"),t.preview||t.selectable||n.classes.add("menu-item-normal"),n.on("mousedown",function(t){t.preventDefault()}),t.menu&&!t.ariaHideMenu&&n.aria("haspopup",!0)},hasMenus:function(){return!!this.settings.menu},showMenu:function(){var e,n=this,t=n.settings,i=n.parent();if(i.items().each(function(t){t!==n&&t.hideMenu()}),t.menu){(e=n.menu)?e.show():((e=t.menu).length?e={type:"menu",items:e}:e.type=e.type||"menu",i.settings.itemDefaults&&(e.itemDefaults=i.settings.itemDefaults),(e=n.menu=_e.create(e).parent(n).renderTo()).reflow(),e.on("cancel",function(t){t.stopPropagation(),n.focus(),e.hide()}),e.on("show hide",function(t){t.control.items&&t.control.items().each(function(t){t.active(t.settings.selected)})}).fire("show"),e.on("hide",function(t){t.control===e&&n.classes.remove("selected")}),e.submenu=!0),e._parentMenu=i,e.classes.add("menu-sub");var r=e.testMoveRel(n.getEl(),n.isRtl()?["tl-tr","bl-br","tr-tl","br-bl"]:["tr-tl","br-bl","tl-tr","bl-br"]);e.moveRel(n.getEl(),r),r="menu-sub-"+(e.rel=r),e.classes.remove(e._lastRel).add(r),e._lastRel=r,n.classes.add("selected"),n.aria("expanded",!0)}},hideMenu:function(){var t=this;return t.menu&&(t.menu.items().each(function(t){t.hideMenu&&t.hideMenu()}),t.menu.hide(),t.aria("expanded",!1)),t},renderHtml:function(){var t,e=this,n=e._id,i=e.settings,r=e.classPrefix,o=e.state.get("text"),s=e.settings.icon,a="",l=i.shortcut,u=e.encode(i.url);function c(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function d(t){var e=i.match||"";return e?t.replace(new RegExp(c(e),"gi"),function(t){return"!mce~match["+t+"]mce~match!"}):t}function f(t){return t.replace(new RegExp(c("!mce~match["),"g"),"<b>").replace(new RegExp(c("]mce~match!"),"g"),"</b>")}return s&&e.parent().classes.add("menu-has-icons"),i.image&&(a=" style=\"background-image: url('"+i.image+"')\""),l&&(l=function(t){var e,n,i={};for(i=h.mac?{alt:"&#x2325;",ctrl:"&#x2318;",shift:"&#x21E7;",meta:"&#x2318;"}:{meta:"Ctrl"},t=t.split("+"),e=0;e<t.length;e++)(n=i[t[e].toLowerCase()])&&(t[e]=n);return t.join("+")}(l)),s=r+"ico "+r+"i-"+(e.settings.icon||"none"),t="-"!==o?'<i class="'+s+'"'+a+"></i>\xa0":"",o=f(e.encode(d(o))),u=f(e.encode(d(u))),'<div id="'+n+'" class="'+e.classes+'" tabindex="-1">'+t+("-"!==o?'<span id="'+n+'-text" class="'+r+'text">'+o+"</span>":"")+(l?'<div id="'+n+'-shortcut" class="'+r+'menu-shortcut">'+l+"</div>":"")+(i.menu?'<div class="'+r+'caret"></div>':"")+(u?'<div class="'+r+'menu-item-link">'+u+"</div>":"")+"</div>"},postRender:function(){var e=this,n=e.settings,t=n.textStyle;if("function"==typeof t&&(t=t.call(this)),t){var i=e.getEl("text");i&&(i.setAttribute("style",t),e._textStyle=t)}return e.on("mouseenter click",function(t){t.control===e&&(n.menu||"click"!==t.type?(e.showMenu(),t.aria&&e.menu.focus(!0)):(e.fire("select"),c.requestAnimationFrame(function(){e.parent().hideAll()})))}),e._super(),e},hover:function(){return this.parent().items().each(function(t){t.classes.remove("selected")}),this.classes.toggle("selected",!0),this},active:function(t){return function(t,e){var n=t._textStyle;if(n){var i=t.getEl("text");i.setAttribute("style",n),e&&(i.style.color="",i.style.backgroundColor="")}}(this,t),void 0!==t&&this.aria("checked",t),this._super(t)},remove:function(){this._super(),this.menu&&this.menu.remove()}}),qr=Dn.extend({Defaults:{classes:"radio",role:"radio"}}),Yr=ge.extend({renderHtml:function(){var t=this,e=t.classPrefix;return t.classes.add("resizehandle"),"both"===t.settings.direction&&t.classes.add("resizehandle-both"),t.canFocus=!1,'<div id="'+t._id+'" class="'+t.classes+'"><i class="'+e+"ico "+e+'i-resize"></i></div>'},postRender:function(){var e=this;e._super(),e.resizeDragHelper=new we(this._id,{start:function(){e.fire("ResizeStart")},drag:function(t){"both"!==e.settings.direction&&(t.deltaX=0),e.fire("Resize",t)},stop:function(){e.fire("ResizeEnd")}})},remove:function(){return this.resizeDragHelper&&this.resizeDragHelper.destroy(),this._super()}});function $r(t){var e="";if(t)for(var n=0;n<t.length;n++)e+='<option value="'+t[n]+'">'+t[n]+"</option>";return e}var Xr=ge.extend({Defaults:{classes:"selectbox",role:"selectbox",options:[]},init:function(t){var n=this;n._super(t),n.settings.size&&(n.size=n.settings.size),n.settings.options&&(n._options=n.settings.options),n.on("keydown",function(t){var e;13===t.keyCode&&(t.preventDefault(),n.parents().reverse().each(function(t){if(t.toJSON)return e=t,!1}),n.fire("submit",{data:e.toJSON()}))})},options:function(t){return arguments.length?(this.state.set("options",t),this):this.state.get("options")},renderHtml:function(){var t,e=this,n="";return t=$r(e._options),e.size&&(n=' size = "'+e.size+'"'),'<select id="'+e._id+'" class="'+e.classes+'"'+n+">"+t+"</select>"},bindStates:function(){var e=this;return e.state.on("change:options",function(t){e.getEl().innerHTML=$r(t.value)}),e._super()}});function jr(t,e,n){return t<e&&(t=e),n<t&&(t=n),t}function Jr(t,e,n){t.setAttribute("aria-"+e,n)}function Gr(t,e){var n,i,r,o,s;"v"===t.settings.orientation?(r="top",i="height",n="h"):(r="left",i="width",n="w"),s=t.getEl("handle"),o=((t.layoutRect()[n]||100)-Ht.getSize(s)[i])*((e-t._minValue)/(t._maxValue-t._minValue))+"px",s.style[r]=o,s.style.height=t.layoutRect().h+"px",Jr(s,"valuenow",e),Jr(s,"valuetext",""+t.settings.previewFilter(e)),Jr(s,"valuemin",t._minValue),Jr(s,"valuemax",t._maxValue)}var Kr=ge.extend({init:function(t){var e=this;t.previewFilter||(t.previewFilter=function(t){return Math.round(100*t)/100}),e._super(t),e.classes.add("slider"),"v"===t.orientation&&e.classes.add("vertical"),e._minValue=bt(t.minValue)?t.minValue:0,e._maxValue=bt(t.maxValue)?t.maxValue:100,e._initValue=e.state.get("value")},renderHtml:function(){var t=this._id,e=this.classPrefix;return'<div id="'+t+'" class="'+this.classes+'"><div id="'+t+'-handle" class="'+e+'slider-handle" role="slider" tabindex="-1"></div></div>'},reset:function(){this.value(this._initValue).repaint()},postRender:function(){var t,e,n,i,r,o,s,a,l,u,c,d,f,h,m=this;t=m._minValue,e=m._maxValue,"v"===m.settings.orientation?(n="screenY",i="top",r="height",o="h"):(n="screenX",i="left",r="width",o="w"),m._super(),function(o,s){function e(t){var e,n,i,r;e=jr(e=(((e=m.value())+(r=n=o))/((i=s)-r)+.05*t)*(i-n)-n,o,s),m.value(e),m.fire("dragstart",{value:e}),m.fire("drag",{value:e}),m.fire("dragend",{value:e})}m.on("keydown",function(t){switch(t.keyCode){case 37:case 38:e(-1);break;case 39:case 40:e(1)}})}(t,e),s=t,a=e,l=m.getEl("handle"),m._dragHelper=new we(m._id,{handle:m._id+"-handle",start:function(t){u=t[n],c=parseInt(m.getEl("handle").style[i],10),d=(m.layoutRect()[o]||100)-Ht.getSize(l)[r],m.fire("dragstart",{value:h})},drag:function(t){var e=t[n]-u;f=jr(c+e,0,d),l.style[i]=f+"px",h=s+f/d*(a-s),m.value(h),m.tooltip().text(""+m.settings.previewFilter(h)).show().moveRel(l,"bc tc"),m.fire("drag",{value:h})},stop:function(){m.tooltip().hide(),m.fire("dragend",{value:h})}})},repaint:function(){this._super(),Gr(this,this.value())},bindStates:function(){var e=this;return e.state.on("change:value",function(t){Gr(e,t.value)}),e._super()}}),Zr=ge.extend({renderHtml:function(){return this.classes.add("spacer"),this.canFocus=!1,'<div id="'+this._id+'" class="'+this.classes+'"></div>'}}),Qr=Ir.extend({Defaults:{classes:"widget btn splitbtn",role:"button"},repaint:function(){var t,e,n=this.getEl(),i=this.layoutRect();return this._super(),t=n.firstChild,e=n.lastChild,Tt(t).css({width:i.w-Ht.getSize(e).width,height:i.h-2}),Tt(e).css({height:i.h-2}),this},activeMenu:function(t){Tt(this.getEl().lastChild).toggleClass(this.classPrefix+"active",t)},renderHtml:function(){var t,e,n=this,i=n._id,r=n.classPrefix,o=n.state.get("icon"),s=n.state.get("text"),a=n.settings,l="";return(t=a.image)?(o="none","string"!=typeof t&&(t=_.window.getSelection?t[0]:t[1]),t=" style=\"background-image: url('"+t+"')\""):t="",o=a.icon?r+"ico "+r+"i-"+o:"",s&&(n.classes.add("btn-has-text"),l='<span class="'+r+'txt">'+n.encode(s)+"</span>"),e="boolean"==typeof a.active?' aria-pressed="'+a.active+'"':"",'<div id="'+i+'" class="'+n.classes+'" role="button"'+e+' tabindex="-1"><button type="button" hidefocus="1" tabindex="-1">'+(o?'<i class="'+o+'"'+t+"></i>":"")+l+'</button><button type="button" class="'+r+'open" hidefocus="1" tabindex="-1">'+(n._menuBtnText?(o?"\xa0":"")+n._menuBtnText:"")+' <i class="'+r+'caret"></i></button></div>'},postRender:function(){var n=this.settings.onclick;return this.on("click",function(t){var e=t.target;if(t.control===this)for(;e;){if(t.aria&&"down"!==t.aria.key||"BUTTON"===e.nodeName&&-1===e.className.indexOf("open"))return t.stopImmediatePropagation(),void(n&&n.call(this,t));e=e.parentNode}}),delete this.settings.onclick,this._super()}}),to=ir.extend({Defaults:{containerClass:"stack-layout",controlClass:"stack-layout-item",endClass:"break"},isNative:function(){return!0}}),eo=Oe.extend({Defaults:{layout:"absolute",defaults:{type:"panel"}},activateTab:function(n){var t;this.activeTabId&&(t=this.getEl(this.activeTabId),Tt(t).removeClass(this.classPrefix+"active"),t.setAttribute("aria-selected","false")),this.activeTabId="t"+n,(t=this.getEl("t"+n)).setAttribute("aria-selected","true"),Tt(t).addClass(this.classPrefix+"active"),this.items()[n].show().fire("showtab"),this.reflow(),this.items().each(function(t,e){n!==e&&t.hide()})},renderHtml:function(){var i=this,t=i._layout,r="",o=i.classPrefix;return i.preRender(),t.preRender(i),i.items().each(function(t,e){var n=i._id+"-t"+e;t.aria("role","tabpanel"),t.aria("labelledby",n),r+='<div id="'+n+'" class="'+o+'tab" unselectable="on" role="tab" aria-controls="'+t._id+'" aria-selected="false" tabIndex="-1">'+i.encode(t.settings.title)+"</div>"}),'<div id="'+i._id+'" class="'+i.classes+'" hidefocus="1" tabindex="-1"><div id="'+i._id+'-head" class="'+o+'tabs" role="tablist">'+r+'</div><div id="'+i._id+'-body" class="'+i.bodyClasses+'">'+t.renderHtml(i)+"</div></div>"},postRender:function(){var i=this;i._super(),i.settings.activeTab=i.settings.activeTab||0,i.activateTab(i.settings.activeTab),this.on("click",function(t){var e=t.target.parentNode;if(e&&e.id===i._id+"-head")for(var n=e.childNodes.length;n--;)e.childNodes[n]===t.target&&i.activateTab(n)})},initLayoutRect:function(){var t,e,n,i=this;e=(e=Ht.getSize(i.getEl("head")).width)<0?0:e,n=0,i.items().each(function(t){e=Math.max(e,t.layoutRect().minW),n=Math.max(n,t.layoutRect().minH)}),i.items().each(function(t){t.settings.x=0,t.settings.y=0,t.settings.w=e,t.settings.h=n,t.layoutRect({x:0,y:0,w:e,h:n})});var r=Ht.getSize(i.getEl("head")).height;return i.settings.minWidth=e,i.settings.minHeight=n+r,(t=i._super()).deltaH+=r,t.innerH=t.h-t.deltaH,t}}),no=ge.extend({init:function(t){var n=this;n._super(t),n.classes.add("textbox"),t.multiline?n.classes.add("multiline"):(n.on("keydown",function(t){var e;13===t.keyCode&&(t.preventDefault(),n.parents().reverse().each(function(t){if(t.toJSON)return e=t,!1}),n.fire("submit",{data:e.toJSON()}))}),n.on("keyup",function(t){n.state.set("value",t.target.value)}))},repaint:function(){var t,e,n,i,r,o=this,s=0;t=o.getEl().style,e=o._layoutRect,r=o._lastRepaintRect||{};var a=_.document;return!o.settings.multiline&&a.all&&(!a.documentMode||a.documentMode<=8)&&(t.lineHeight=e.h-s+"px"),i=(n=o.borderBox).left+n.right+8,s=n.top+n.bottom+(o.settings.multiline?8:0),e.x!==r.x&&(t.left=e.x+"px",r.x=e.x),e.y!==r.y&&(t.top=e.y+"px",r.y=e.y),e.w!==r.w&&(t.width=e.w-i+"px",r.w=e.w),e.h!==r.h&&(t.height=e.h-s+"px",r.h=e.h),o._lastRepaintRect=r,o.fire("repaint",{},!1),o},renderHtml:function(){var e,t,n=this,i=n.settings;return e={id:n._id,hidefocus:"1"},R.each(["rows","spellcheck","maxLength","size","readonly","min","max","step","list","pattern","placeholder","required","multiple"],function(t){e[t]=i[t]}),n.disabled()&&(e.disabled="disabled"),i.subtype&&(e.type=i.subtype),(t=Ht.create(i.multiline?"textarea":"input",e)).value=n.state.get("value"),t.className=n.classes.toString(),t.outerHTML},value:function(t){return arguments.length?(this.state.set("value",t),this):(this.state.get("rendered")&&this.state.set("value",this.getEl().value),this.state.get("value"))},postRender:function(){var e=this;e.getEl().value=e.state.get("value"),e._super(),e.$el.on("change",function(t){e.state.set("value",t.target.value),e.fire("change",t)})},bindStates:function(){var e=this;return e.state.on("change:value",function(t){e.getEl().value!==t.value&&(e.getEl().value=t.value)}),e.state.on("change:disabled",function(t){e.getEl().disabled=t.value}),e._super()},remove:function(){this.$el.off(),this._super()}}),io=function(){return{Selector:Ft,Collection:qt,ReflowQueue:Zt,Control:ae,Factory:_e,KeyboardNavigation:Ce,Container:Me,DragHelper:we,Scrollable:Ne,Panel:Oe,Movable:he,Resizable:We,FloatPanel:ze,Window:$e,MessageBox:Ge,Tooltip:me,Widget:ge,Progress:pe,Notification:be,Layout:Mn,AbsoluteLayout:Nn,Button:On,ButtonGroup:Pn,Checkbox:Dn,ComboBox:Bn,ColorBox:Ln,PanelButton:In,ColorButton:Fn,ColorPicker:Vn,Path:Yn,ElementPath:$n,FormItem:Xn,Form:jn,FieldSet:Jn,FilePicker:tr,FitLayout:er,FlexLayout:nr,FlowLayout:ir,FormatControls:Or,GridLayout:Wr,Iframe:Pr,InfoBox:Dr,Label:Ar,Toolbar:Br,MenuBar:Lr,MenuButton:Ir,MenuItem:Vr,Throbber:zr,Menu:Fr,ListBox:Ur,Radio:qr,ResizeHandle:Yr,SelectBox:Xr,Slider:Kr,Spacer:Zr,SplitButton:Qr,StackLayout:to,TabPanel:eo,TextBox:no,DropZone:qn,BrowseButton:Wn}},ro=function(n){n.ui?R.each(io(),function(t,e){n.ui[e]=t}):n.ui=io()};R.each(io(),function(t,e){_e.add(e,t)}),ro(window.tinymce?window.tinymce:{}),r.add("inlite",function(t){var e=Sn();return Or.setup(t),_n(t,e),Ke(t,e)})}(window);