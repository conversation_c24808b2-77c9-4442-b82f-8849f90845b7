!function(a,b){a.PluginManager.add("wpview",function(c){function d(){}function e(a){return c.dom.hasClass(a,"wpview")}function f(a){function b(a,b){return"<p>"+window.decodeURIComponent(b)+"</p>"}return a?a.replace(/<div[^>]+data-wpview-text="([^"]+)"[^>]*>(?:\.|[\s\S]+?wpview-end[^>]+>\s*<\/span>\s*)?<\/div>/g,b).replace(/<p[^>]+data-wpview-marker="([^"]+)"[^>]*>[\s\S]*?<\/p>/g,b):a}return b&&b.mce&&b.mce.views?(c.on("init",function(){var a=window.MutationObserver||window.WebKitMutationObserver;a&&new a(function(){c.fire("wp-body-class-change")}).observe(c.getBody(),{attributes:!0,attributeFilter:["class"]}),c.on("wp-body-class-change",function(){var a=c.getBody().className;c.$('iframe[class="wpview-sandbox"]').each(function(b,c){if(!c.src||'javascript:""'===c.src)try{c.contentWindow.document.body.className=a}catch(d){}})})}),c.on("beforesetcontent",function(a){var d;if(a.selection||b.mce.views.unbind(),a.content){if(!a.load&&(d=c.selection.getNode(),d&&d!==c.getBody()&&/^\s*https?:\/\/\S+\s*$/i.test(a.content))){if(d=c.dom.getParent(d,"p"),!d||!/^[\s\uFEFF\u00A0]*$/.test(c.$(d).text()||""))return;d.innerHTML=""}a.content=b.mce.views.setMarkers(a.content,c)}}),c.on("setcontent",function(){b.mce.views.render()}),c.on("preprocess hide",function(a){c.$("div[data-wpview-text], p[data-wpview-marker]",a.node).each(function(a,b){b.innerHTML="."})},!0),c.on("postprocess",function(a){a.content=f(a.content)}),c.on("beforeaddundo",function(a){a.level.content=f(a.level.content)}),c.on("drop objectselected",function(a){e(a.targetClone)&&(a.targetClone=c.getDoc().createTextNode(window.decodeURIComponent(c.dom.getAttrib(a.targetClone,"data-wpview-text"))))}),c.on("pastepreprocess",function(b){var c=b.content;c&&(c=a.trim(c.replace(/<[^>]+>/g,"")),/^https?:\/\/\S+$/i.test(c)&&(b.content=c))}),c.on("resolvename",function(a){e(a.target)&&(a.name=c.dom.getAttrib(a.target,"data-wpview-type")||"object")}),c.on("click keyup",function(){var a=c.selection.getNode();e(a)&&c.dom.getAttrib(a,"data-mce-selected")&&a.setAttribute("data-mce-selected","2")}),c.addButton("wp_view_edit",{tooltip:"Edit|button",icon:"dashicon dashicons-edit",onclick:function(){var a=c.selection.getNode();e(a)&&b.mce.views.edit(c,a)}}),c.addButton("wp_view_remove",{tooltip:"Remove",icon:"dashicon dashicons-no",onclick:function(){c.fire("cut")}}),c.once("preinit",function(){var a;c.wp&&c.wp._createToolbar&&(a=c.wp._createToolbar(["wp_view_edit","wp_view_remove"]),c.on("wptoolbar",function(b){!b.collapsed&&e(b.element)&&(b.toolbar=a)}))}),c.wp=c.wp||{},c.wp.getView=d,c.wp.setViewCursor=d,{getView:d}):{getView:d}})}(window.tinymce,window.wp);