/*
object-assign
(c) <PERSON><PERSON>
@license MIT
*/

/*!
  Copyright (c) 2018 <PERSON>.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/

/*!
 * @overview es6-promise - a tiny implementation of Promises/A+.
 * @copyright Copyright (c) 2014 <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and contributors (Conversion to ES6 API by <PERSON>)
 * @license   Licensed under MIT license
 *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE
 * @version   v4.2.8+1e68dce6
 */

/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> <http://feross.org>
 * @license  MIT
 */

/*!
CSSLint v1.0.4
Copyright (c) 2016 <PERSON> and <PERSON>. All rights reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the 'Software'), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

*/

/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

/*! https://mths.be/punycode v1.4.1 by @mathias */

/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */

/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */

/**
       * Generates className value based on the args provided. Takes any number of args which can
       * be a string or an object. The argument `foo` is short for `{ foo: true }`. If the value
       * associated with a given key is falsy, the key won't be included in the output.
       *
       * @example
       * classNames('foo', 'bar');                 // => 'foo bar'
       * classNames('foo', { bar: true });         // => 'foo bar'
       * classNames({ 'foo-bar': true });          // => 'foo-bar'
       * classNames({ 'foo-bar': false });         // => ''
       * classNames({ foo: true }, { bar: true }); // => 'foo bar'
       * classNames({ foo: true, bar: true });     // => 'foo bar'
       *
       * @param {...string | object.<string, boolean>}
       *
       * @link https://github.com/JedWatson/classnames
       * @copyright 2017 Jed Watson
       * @license MIT
       * @memberof API.Utils
       * @function
       *
       * @returns {string}
       */

/**
       * Lodash - A modern JavaScript utility library delivering modularity, performance & extras.
       *
       * @memberof API.Utils
       * @function
       * @link https://github.com/lodash/lodash
       * @copyright JS Foundation and other contributors <https://js.foundation/>
       * @license MIT
       */

/**
 * This component allows you to use dynamically registered components inside of render
 * methods--including components external to this React application. Instead of explicitly
 * rendering a component, like `<SomeComponent>`, you can use `ETBuilderInjectedComponent`:
 *
 * ```jsx
 * <ETBuilderInjectedComponent matching={slug: 'some_component'} />
 * ```.
 *
 * `ETBuilderInjectedComponent` will look up the component registered with that slug in the
 * {@link ETBuilderComponentRegistry} and render it, while passing along the props you provided.
 *
 * If no matching component is found, the `ETBuilderInjectedComponent` renders an empty `<div>`.
 *
 * `ETBuilderInjectedComponent` monitors the Component Registry for changes. If a new component
 * is registered that matches the descriptor you provided, `ETBuilderInjectedComponent` will refresh.
 *
 * @property {object}    props            Contains the props that were defined by the caller of this component.
 * @property {object}    props.matching   Set of descriptors for retrieving the component to be displayed from
 *                                    {@link ETBuilderComponentRegistry.findComponentBySlug}.
 * @property {Component} [props.fallback] An optional fallback component in case there are no matching
 *                                    components in the Component Registry.
 * @property {string[]}  [props.methods]  A list of methods that should be implemented by the registered
 *                                    component instance. If these are not implemented, the component
 *                                    won't be rendered.
 *
 * Based on original concept and code from [Nylas Mail]{@link https://github.com/nylas/nylas-mail}.
 *
 * @copyright © 2017 Elegant Themes, Inc.
 * @copyright © 2017 Nylas, Inc.
 * @license   GPL-3.0
 */

/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/** @license React v16.14.0
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
