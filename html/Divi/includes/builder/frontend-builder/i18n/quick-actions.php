<?php
/**
 * Internationalization
 *
 * @package Divi
 */

return array(
	'%s Module'                                 => esc_html__( '%s Module', 'et_builder' ),
	'A Module'                                  => esc_html__( 'A Module', 'et_builder' ),
	'A Row'                                     => esc_html__( 'A Row', 'et_builder' ),
	'A Section'                                 => esc_html__( 'A Section', 'et_builder' ),
	'Background Settings'                       => esc_html__( 'Background Settings', 'et_builder' ),
	'Build On The Front End'                    => esc_html__( 'Build On The Front End', 'et_builder' ),
	'Builder'                                   => esc_html__( 'Builder', 'et_builder' ),
	'Builder Default Interaction Mode'          => esc_html__( 'Builder Default Interaction Mode', 'et_builder' ),
	'Builder History'                           => esc_html__( 'Builder History', 'et_builder' ),
	'Builder Interface Animations'              => esc_html__( 'Builder Interface Animations', 'et_builder' ),
	'Builder Keyboard Shortcuts'                => esc_html__( 'Builder Keyboard Shortcuts', 'et_builder' ),
	'Builder Settings'                          => esc_html__( 'Builder Settings', 'et_builder' ),
	'Clear'                                     => esc_html__( 'Clear', 'et_builder' ),
	'Clear , Save to Library, Import, Export'   => esc_html__( 'Clear , Save to Library, Import, Export', 'et_builder' ),
	'Clear Layout'                              => esc_html__( 'Clear Layout', 'et_builder' ),
	'Click Mode'                                => esc_html__( 'Click Mode', 'et_builder' ),
	'Close'                                     => esc_html__( 'Close', 'et_builder' ),
	'Color Palette Settings'                    => esc_html__( 'Color Palette Settings', 'et_builder' ),
	'Custom CSS Settings'                       => esc_html__( 'Custom CSS Settings', 'et_builder' ),
	'Customize Builder Settings Toolbar'        => esc_html__( 'Customize Builder Settings Toolbar', 'et_builder' ),
	'Customize Settings Toolbar'                => esc_html__( 'Customize Settings Toolbar', 'et_builder' ),
	'Default Interaction Mode'                  => esc_html__( 'Default Interaction Mode', 'et_builder' ),
	'Desktop'                                   => et_builder_i18n( 'Desktop' ),
	'Desktop View'                              => esc_html__( 'Desktop View', 'et_builder' ),
	'Divi Documentation'                        => esc_html__( 'Divi Documentation', 'et_builder' ),
	'Elegant Themes Support'                    => esc_html__( 'Elegant Themes Support', 'et_builder' ),
	'Existing Page Layout'                      => esc_html__( 'Existing Page Layout', 'et_builder' ),
	'Existing Post Layout'                      => esc_html__( 'Existing Post Layout', 'et_builder' ),
	'Exit'                                      => esc_html__( 'Exit', 'et_builder' ),
	'Exit Builder'                              => esc_html__( 'Exit Builder', 'et_builder' ),
	'Exit to Backend'                           => esc_html__( 'Exit to Backend', 'et_builder' ),
	'Exit to Backend Builder'                   => esc_html__( 'Exit to Backend Builder', 'et_builder' ),
	'Export'                                    => esc_html__( 'Export', 'et_builder' ),
	'Export Layout'                             => esc_html__( 'Export Layout', 'et_builder' ),
	'External Article'                          => esc_html__( 'External Article', 'et_builder' ),
	'External Link'                             => esc_html__( 'External Link', 'et_builder' ),
	'Fullwidth Section'                         => esc_html__( 'Fullwidth Section', 'et_builder' ),
	'Go To'                                     => esc_html__( 'Go To', 'et_builder' ),
	'Go To A Module'                            => esc_html__( 'Go To A Module', 'et_builder' ),
	'Go To A Row'                               => esc_html__( 'Go To A Row', 'et_builder' ),
	'Go To A Section'                           => esc_html__( 'Go To A Section', 'et_builder' ),
	'Go To a Section, Row or Module'            => esc_html__( 'Go To a Section, Row or Module', 'et_builder' ),
	'Grid Mode'                                 => esc_html__( 'Grid Mode', 'et_builder' ),
	'Help'                                      => esc_html__( 'Help', 'et_builder' ),
	'Help Video'                                => esc_html__( 'Help Video', 'et_builder' ),
	'History'                                   => esc_html__( 'History', 'et_builder' ),
	'History State Interval'                    => esc_html__( 'History State Interval', 'et_builder' ),
	'Hover Mode'                                => esc_html__( 'Hover Mode', 'et_builder' ),
	'Import'                                    => esc_html__( 'Import', 'et_builder' ),
	'Import Layout'                             => esc_html__( 'Import Layout', 'et_builder' ),
	'Insert'                                    => esc_html__( 'Insert', 'et_builder' ),
	'Insert %s'                                 => esc_html__( 'Insert %s', 'et_builder' ),
	'Insert %s Module'                          => esc_html__( 'Insert %s Module', 'et_builder' ),
	'Insert Existing Page Layout'               => esc_html__( 'Insert Existing Page Layout', 'et_builder' ),
	'Insert Existing Post Layout'               => esc_html__( 'Insert Existing Post Layout', 'et_builder' ),
	'Insert Premade Layout'                     => esc_html__( 'Insert Premade Layout', 'et_builder' ),
	'Insert Saved Layout'                       => esc_html__( 'Insert Saved Layout', 'et_builder' ),
	'Insert Saved Module'                       => esc_html__( 'Insert Saved Module', 'et_builder' ),
	'Insert Saved Row'                          => esc_html__( 'Insert Saved Row', 'et_builder' ),
	'Insert Saved Section'                      => esc_html__( 'Insert Saved Section', 'et_builder' ),
	'Interface Animations'                      => esc_html__( 'Interface Animations', 'et_builder' ),
	'Keyboard Shortcuts'                        => esc_html__( 'Keyboard Shortcuts', 'et_builder' ),
	'Layout'                                    => et_builder_i18n( 'Layout' ),
	'Layout, Section, Row, Module'              => esc_html__( 'Layout, Section, Row, Module', 'et_builder' ),
	'Library'                                   => esc_html__( 'Library', 'et_builder' ),
	'Load From Library'                         => esc_html__( 'Load From Library', 'et_builder' ),
	'Open'                                      => esc_html__( 'Open', 'et_builder' ),
	'Open %s'                                   => esc_html__( 'Open %s', 'et_builder' ),
	'Open Settings'                             => esc_html__( 'Open Settings', 'et_builder' ),
	'Page'                                      => esc_html__( 'Page', 'et_builder' ),
	'Page Background Settings'                  => esc_html__( 'Page Background Settings', 'et_builder' ),
	'Page Color Palette Settings'               => esc_html__( 'Page Color Palette Settings', 'et_builder' ),
	'Page Creation Flow'                        => esc_html__( 'Page Creation Flow', 'et_builder' ),
	'Page Custom CSS Settings'                  => esc_html__( 'Page Custom CSS Settings', 'et_builder' ),
	'Page Performance Settings'                 => esc_html__( 'Page Performance Settings', 'et_builder' ),
	'Page Settings'                             => esc_html__( 'Page Settings', 'et_builder' ),
	'Page Spacing Settings'                     => esc_html__( 'Page Spacing Settings', 'et_builder' ),
	'Performance Settings'                      => esc_html__( 'Performance Settings', 'et_builder' ),
	'Phone'                                     => et_builder_i18n( 'Phone' ),
	'Phone View'                                => esc_html__( 'Phone View', 'et_builder' ),
	'Portability'                               => esc_html__( 'Portability', 'et_builder' ),
	'Premade Layout'                            => esc_html__( 'Premade Layout', 'et_builder' ),
	'Preview'                                   => esc_html__( 'Preview', 'et_builder' ),
	'Preview Page'                              => esc_html__( 'Preview Page', 'et_builder' ),
	'Publish'                                   => esc_html__( 'Publish', 'et_builder' ),
	'Publish Page'                              => esc_html__( 'Publish Page', 'et_builder' ),
	'Redo'                                      => esc_html__( 'Redo', 'et_builder' ),
	'Regular Section'                           => esc_html__( 'Regular Section', 'et_builder' ),
	'Role Editor'                               => esc_html__( 'Role Editor', 'et_builder' ),
	'Row'                                       => esc_html__( 'Row', 'et_builder' ),
	'Save'                                      => esc_html__( 'Save', 'et_builder' ),
	'Save Draft'                                => esc_html__( 'Save Draft', 'et_builder' ),
	'Save Layout to Library'                    => esc_html__( 'Save Layout to Library', 'et_builder' ),
	'Save Page'                                 => esc_html__( 'Save Page', 'et_builder' ),
	'Save to Library'                           => esc_html__( 'Save to Library', 'et_builder' ),
	'Saved Layout'                              => esc_html__( 'Saved Layout', 'et_builder' ),
	'Saved Module'                              => esc_html__( 'Saved Module', 'et_builder' ),
	'Saved Row'                                 => esc_html__( 'Saved Row', 'et_builder' ),
	'Saved Section'                             => esc_html__( 'Saved Section', 'et_builder' ),
	'Search'                                    => esc_html__( 'Search', 'et_builder' ),
	'Search Documentation or Get Support'       => esc_html__( 'Search Documentation or Get Support', 'et_builder' ),
	'Settings'                                  => esc_html__( 'Settings', 'et_builder' ),
	'Settings Modal Default Position'           => esc_html__( 'Settings Modal Default Position', 'et_builder' ),
	'Settings, Background, Color Palette, Gutters, Custom CSS, Performance' => esc_html__( 'Settings, Background, Color Palette, Gutters, Custom CSS, Performance', 'et_builder' ),
	'Settings, History, Exit, Shortcuts'        => esc_html__( 'Settings, History, Exit, Shortcuts', 'et_builder' ),
	'Settings, Pages, Posts, Custom Post Types' => esc_html__( 'Settings, Pages, Posts, Custom Post Types', 'et_builder' ),
	'Show Disabled Modules At 50% Opacity'      => esc_html__( 'Show Disabled Modules At 50% Opacity', 'et_builder' ),
	'Spacing Settings'                          => esc_html__( 'Spacing Settings', 'et_builder' ),
	'Specialty Section'                         => esc_html__( 'Specialty Section', 'et_builder' ),
	'Split Testing'                             => esc_html__( 'Split Testing', 'et_builder' ),
	'Tablet'                                    => et_builder_i18n( 'Tablet' ),
	'Tablet View'                               => esc_html__( 'Tablet View', 'et_builder' ),
	'Theme Customizer'                          => esc_html__( 'Theme Customizer', 'et_builder' ),
	'Theme Options'                             => esc_html__( 'Theme Options', 'et_builder' ),
	'Undo'                                      => esc_html__( 'Undo', 'et_builder' ),
	'View'                                      => esc_html__( 'View', 'et_builder' ),
	'Wireframe Mode'                            => esc_html__( 'Wireframe Mode', 'et_builder' ),
	'Wireframe, Desktop, Tablet, Phone, Click Mode, Grid Mode, Hover Mode, Zoom' => esc_html__( 'Wireframe, Desktop, Tablet, Phone, Click Mode, Grid Mode, Hover Mode, Zoom', 'et_builder' ),
	'Zoom In'                                   => esc_html__( 'Zoom In', 'et_builder' ),
	'Zoom Out'                                  => esc_html__( 'Zoom Out', 'et_builder' ),
);
