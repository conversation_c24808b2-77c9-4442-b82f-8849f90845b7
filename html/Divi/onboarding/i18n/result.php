<?php
/**
 * This file is used to define the translation strings that are used in the onboarding result.
 *
 * @package Divi
 */

return [
	'Your Site Is Ready!'                    => esc_html__( 'Your Site Is Ready!', 'Divi' ),
	'Result Description'                     => esc_html__( 'Your website is ready to go. Below is a list of pages and templates that were created. Next, visit your website and jump into the Divi Builder to edit your content.', 'Divi' ),
	'View My Site'                           => esc_html__( 'View My Site', 'Divi' ),
	'Website Pages'                          => esc_html__( 'Website Pages', 'Divi' ),
	'Delete Page'                            => esc_html__( 'Delete Page', 'Divi' ),
	'Manage Website Pages'                   => esc_html__( 'Manage Website Pages', 'Divi' ),
	'Add New Page'                           => esc_html__( 'Add New Page', 'Divi' ),
	'Manage Theme Templates'                 => esc_html__( 'Manage Theme Templates', 'Divi' ),
	'Tutorials & Documentation'              => esc_html__( 'Tutorials & Documentation', 'Divi' ),
	'Using the Divi Builder'                 => esc_html__( 'Using the Divi Builder', 'Divi' ),
	'Configuring Theme Options'              => esc_html__( 'Configuring Theme Options', 'Divi' ),
	'Managing Website Styles'                => esc_html__( 'Managing Website Styles', 'Divi' ),
	'Using the Divi Theme Builder'           => esc_html__( 'Using the Divi Theme Builder', 'Divi' ),
	'Using the Divi Library'                 => esc_html__( 'Using the Divi Library', 'Divi' ),
	'Using Divi AI'                          => esc_html__( 'Using Divi AI', 'Divi' ),
	'Using Divi Cloud'                       => esc_html__( 'Using Divi Cloud', 'Divi' ),
	'Browse All Divi Docs'                   => esc_html__( 'Browse All Divi Docs', 'Divi' ),
	'Theme Templates'                        => esc_html__( 'Theme Templates', 'Divi' ),
	'Delete'                                 => esc_html__( 'Delete', 'Divi' ),
	'Are you sure want to remove this page?' => esc_html__( 'Are you sure want to remove this page?', 'Divi' ),
	'cancel'                                 => esc_html__( 'Cancel', 'Divi' ),
	'Generate Page'                          => esc_html__( 'Generate Page', 'Divi' ),
	'Page Title'                             => esc_html__( 'Page Title', 'Divi' ),
	'Tell Divi AI What'                      => esc_html__( 'Describe The Page You Want Divi AI To Create', 'Divi' ),
	'Enter which type'                       => esc_html__( 'Enter which type of page you want.', 'Divi' ),
	'Enter your page title'                  => esc_html__( 'Enter your page title', 'Divi' ),
	'Example: Also add'                      => esc_html__( 'Example: Create a services page that describes our solar installation services, including testimonials and a contact form to collect leads', 'Divi' ),
	'Generating...'                          => esc_html__( 'Generating...', 'Divi' ),
	'Generating Your %1$s Page'              => esc_html__( 'Generating Your %1$s Page', 'Divi' ),
	'Fetching Pages...'                      => esc_html__( 'Fetching Pages...', 'Divi' ),
	'Generating Page Completed!'             => esc_html__( 'Page Generated Successfully', 'Divi' ),
	'%1$s Images Used From Unsplash'         => esc_html__( '%1$s Images Used From Unsplash', 'Divi' ),
	'WooCommerce plugin is not installed'    => esc_html__( 'The WooCommerce plugin failed to install during the website generation process. You must install and activate the plugin manually.', 'Divi' ),
	'WooCommerce plugin is not activated'    => esc_html__( 'The WooCommerce plugin was installed but failed to activate during the website generation process. Visit the Plugins page to activate WooCommerce manually.', 'Divi' ),
	'WooCommerce Plugin Status'              => esc_html__( 'WooCommerce Plugin Status', 'Divi' ),
	'Install WooCommerce Plugin'             => esc_html__( 'Install WooCommerce Plugin', 'Divi' ),
	'Activate WooCommerce Plugin'            => esc_html__( 'Activate WooCommerce Plugin', 'Divi' ),
];
