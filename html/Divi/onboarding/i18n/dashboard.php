<?php
/**
 * This file is used to define the translation strings that are used in the onboarding.
 *
 * @package Divi
 */

return array(
	'Divi Overview'                                  => esc_html__( 'Divi Overview', 'Divi' ),
	'Your Divi license key is active'                => esc_html__( 'Your Divi license key is active', 'Divi' ),
	'Your Divi license key has expired'              => esc_html__( 'Your Divi license key has expired', 'Divi' ),
	'Your Divi license key is invalid'               => esc_html__( 'Your Divi license key is invalid', 'Divi' ),
	'Your Elegant Themes username is invalid'        => esc_html__( 'Your Elegant Themes username is invalid', 'Divi' ),
	'This website is linked to ...'                  => esc_html__( 'This website is linked to your Divi subscriptions and ready to receive the latest Divi updates.', 'Divi' ),
	'Invalid Username'                               => esc_html__( 'The Elegant Themes username you entered is invalid. Please enter a valid username to receive product updates. If you forgot your username you can', 'Divi' ),
	'Renew text'                                     => esc_html__( 'Your license key has expired.', 'Divi' ),
	'Invalid api key'                                => esc_html__( 'The Elegant Themes API key you entered is invalid. Please make sure that your API has been entered correctly and that it is enabled in your account.', 'Divi' ),
	'Enter new API Key'                              => esc_html__( 'Enter new API Key', 'Divi' ),
	'Enable API Key'                                 => esc_html__( 'Enable API Key', 'Divi' ),
	'Renew Subscription'                             => esc_html__( 'Renew Subscription', 'Divi' ),
	'Divi is up to date'                             => esc_html__( 'Divi is up to date', 'Divi' ),
	'Your Divi is outdated'                          => esc_html__( 'Your Divi is outdated', 'Divi' ),
	'You are using the latest version of Divi.'      => esc_html__( 'You are using the latest version of Divi.', 'Divi' ),
	'You are using outdated version of Divi'         => esc_html__( 'You are using outdated version of Divi. New version is', 'Divi' ),
	'Pages Built With Divi'                          => esc_html__( 'Pages Built With Divi', 'Divi' ),
	'Manage Pages'                                   => esc_html__( 'Manage Pages', 'Divi' ),
	'Posts Built With Divi'                          => esc_html__( 'Posts Built With Divi', 'Divi' ),
	'Manage Posts'                                   => esc_html__( 'Manage Posts', 'Divi' ),
	'Divi Theme Templates'                           => esc_html__( 'Divi Theme Templates', 'Divi' ),
	'Manage Templates'                               => esc_html__( 'Manage Templates', 'Divi' ),
	'Documentation'                                  => esc_html__( 'Documentation', 'Divi' ),
	'Using the Divi Builder'                         => esc_html__( 'Using the Divi Builder', 'Divi' ),
	'Using the Divi Theme Builder'                   => esc_html__( 'Using the Divi Theme Builder', 'Divi' ),
	'Using Divi AI'                                  => esc_html__( 'Using Divi AI', 'Divi' ),
	'Managing Website Styles'                        => esc_html__( 'Managing Website Styles', 'Divi' ),
	'Browse All Divi Docs'                           => esc_html__( 'Browse All Divi Docs', 'Divi' ),
	'Let’s Build Your Divi Website!'                 => esc_html__( 'Let’s Build Your Divi Website!', 'Divi' ),
	'Use Divi Quick Sites to set up your site ...'   => esc_html__( 'Use Divi Quick Sites to set up your site in less than 2 minutes. We will generate pages and content, set up your website info, and configure your menu, templates, styles and presets automatically. You can build your website using AI, or pick from handcrafted Starter Sites.', 'Divi' ),
	'Use Pre-Made Starter Site'                      => esc_html__( 'Use Pre-Made Starter Site', 'Divi' ),
	'Pick a hand-crafted Starter Site ...'           => esc_html__( 'Pick a handcrafted Starter Site created by our team. Then, jump into your finished website and customize the content.', 'Divi' ),
	'Select a Website Template'                      => esc_html__( 'Select a Website Template', 'Divi' ),
	'Generate Your Site With AI'                     => esc_html__( 'Generate Your Site With AI', 'Divi' ),
	'Simply describe your website ...'               => esc_html__( 'Simply describe your website, sit back, relax, and let Divi AI create your entire website for you in less than 2 minutes.', 'Divi' ),
	'Support'                                        => esc_html__( 'Support', 'Divi' ),
	'Chat With An Expert'                            => esc_html__( 'Chat With An Expert', 'Divi' ),
	'Learn About VIP Support'                        => esc_html__( 'Learn About VIP Support', 'Divi' ),
	'Divi Quick Sites'                               => esc_html__( 'Divi Quick Sites', 'Divi' ),
	'Generate A New Site'                            => esc_html__( 'Generate A New Site', 'Divi' ),
	'Need help getting started with Divi ...'        => esc_html__( 'Need help getting started with Divi? Our support team is available around the clock to help you with anything and everything Divi related.', 'Divi' ),
	'Use Divi Quick Sites to generate a new ...'     => esc_html__( 'Use Divi Quick Sites to generate a new website using Divi AI or premade Divi Starter Sites. Quick Sites will set up your entire site including custom pages, menu items, Theme Builder templates and more.', 'Divi' ),
	'Level Up Your Divi Toolkit'                     => esc_html__( 'Level Up Your Divi Toolkit', 'Divi' ),
	'Divi AI'                                        => esc_html__( 'Divi AI', 'Divi' ),
	'Harness the power of AI ...'                    => esc_html__( 'Harness the power of AI and unlock unlimited text, image, code, layout and website generation.', 'Divi' ),
	'Get access to hundreds of new Divi modules ...' => esc_html__( 'Get access to hundreds of new Divi modules and thousands of additional layouts and child themes.', 'Divi' ),
	'Save your Divi assets to the cloud ...'         => esc_html__( 'Save your Divi assets to the cloud and access them from any website. Collaborate with your team in the cloud.', 'Divi' ),
	'Invite your team members ...'                   => esc_html__( 'Invite your team members and clients and give them full access to your entire suite of Divi services.', 'Divi' ),
	'Get live chat support ...'                      => esc_html__( 'Get live chat support with response times under 30 minutes plus bigger discounts in the Divi Marketplace.', 'Divi' ),
	'Upgrade your Divi membership ...'               => esc_html__( 'Upgrade your Divi membership to Lifetime and use Divi on unlimited websites forever.', 'Divi' ),
	'Learn About Divi AI'                            => esc_html__( 'Learn About Divi AI', 'Divi' ),
	'Divi Marketplace'                               => esc_html__( 'Divi Marketplace', 'Divi' ),
	'Browse The Marketplace'                         => esc_html__( 'Browse The Marketplace', 'Divi' ),
	'Divi Cloud'                                     => esc_html__( 'Divi Cloud', 'Divi' ),
	'Learn About Divi Cloud'                         => esc_html__( 'Learn About Divi Cloud', 'Divi' ),
	'Divi Teams'                                     => esc_html__( 'Divi Teams', 'Divi' ),
	'Learn About Divi Teams'                         => esc_html__( 'Learn About Divi Teams', 'Divi' ),
	'Divi VIP'                                       => esc_html__( 'Divi VIP', 'Divi' ),
	'Learn About Divi VIP'                           => esc_html__( 'Learn About Divi VIP', 'Divi' ),
	'Divi Lifetime'                                  => esc_html__( 'Divi Lifetime', 'Divi' ),
	'Error'                                          => esc_html__( 'Error', 'Divi' ),
	'Get Divi For Life'                              => esc_html__( 'Get Divi For Life', 'Divi' ),
	'Choose A Starter Site'                          => esc_html__( 'Choose A Starter Site', 'Divi' ),
	'Pick the starter site ...'                      => esc_html__( 'Pick the starter site that best suits your website. Every Starter Site can be used for any type of website. Once your website is generated, you can easily customize the content and styles using the Divi Builder.', 'Divi' ),
	'Start With %1$s'                                => esc_html__( 'Start With %1$s', 'Divi' ),
	'Preview %1$s'                                   => esc_html__( 'Preview %1$s', 'Divi' ),
	'Customize your website'                         => esc_html__( 'Customize Your Website and Select Your Pages', 'Divi' ),
	'Input your website information and select ...'  => esc_html__( 'Input your website information and select which pages you want to have on your website.', 'Divi' ),
	'Site name'                                      => esc_html__( 'Site Name', 'Divi' ),
	'Site Slogan'                                    => esc_html__( 'Site Slogan', 'Divi' ),
	'Logo'                                           => esc_html__( 'Logo', 'Divi' ),
	'Upload a logo image ...'                        => esc_html__( 'Upload a logo image to be used in your website header and footer.', 'Divi' ),
	'Pages'                                          => esc_html__( 'Pages', 'Divi' ),
	'Select the pages ...'                           => esc_html__( 'Select the pages that you want to add to your website. You can select from pre-build pages with custom layouts, and add your own pages that will be built using a simple template.', 'Divi' ),
	'Shop (selecting this will install WooCommerce)' => esc_html__( 'Shop (selecting this will install WooCommerce)', 'Divi' ),
	'Cancel'                                         => esc_html__( 'Cancel', 'Divi' ),
	'Skip This Step & Build Manually'                => esc_html__( 'Skip This Step & Build Manually', 'Divi' ),
	'Remove'                                         => esc_html__( 'Remove', 'Divi' ),
	'Change Image'                                   => esc_html__( 'Change Image', 'Divi' ),
	'Upload your logo (optional)'                    => esc_html__( 'Upload your logo (optional)', 'Divi' ),
	'Building Your Website'                          => esc_html__( 'Building Your Website', 'Divi' ),
	'Your website is being created ...'              => esc_html__( 'Your website is being created! Sit back and relax while Divi Quick Sites generates content and configures your menu, templates, styles and presets.', 'Divi' ),
	'Installing WooCommerce'                         => esc_html__( 'Installing WooCommerce', 'Divi' ),
	'Activating WooCommerce'                         => esc_html__( 'Activating WooCommerce', 'Divi' ),
	'Creating Your Menu'                             => esc_html__( 'Creating Your Menu', 'Divi' ),
	'Website Build Complete!'                        => esc_html__( 'Website Build Complete!', 'Divi' ),
	'Creating %1$s Page'                             => esc_html__( 'Importing %1$s Page', 'Divi' ),
	'Updating Website Information'                   => esc_html__( 'Updating Website Information', 'Divi' ),
	'Importing Theme Builder'                        => esc_html__( 'Importing Theme Builder', 'Divi' ),
	'Saving Theme Builder'                           => esc_html__( 'Saving Theme Builder', 'Divi' ),
	'Generating Site Outline'                        => esc_html__( 'Generating Site Outline', 'Divi' ),
	'Login to activate your license'                 => esc_html__( 'Login to activate your license', 'Divi' ),
	'Welcome to Divi!'                               => esc_html__( 'Welcome to Divi!', 'Divi' ),
	'Login to activate your license'                 => esc_html__( 'Login to activate your license', 'Divi' ),
	'Welcome Description'                            => esc_html__( 'Divi has been installed and activated. Next, you must activate your license so that your website can receive updates. Click the button below to log in to your Elegant Themes account and automatically activate this installation.', 'Divi' ),
	'Generate My Website'                            => esc_html__( 'Generate My Website', 'Divi' ),
	'Tell Us About Your Website'                     => esc_html__( 'Tell Us About Your Website', 'Divi' ),
	'Input your website information ...'             => esc_html__( 'Input your website information and tell Divi AI about your website or business. The information you provide will be used to generate content, images and make design decisions.', 'Divi' ),
	'Tell Divi AI About Your Website'                => esc_html__( 'Tell Divi AI About Your Website', 'Divi' ),
	'Install and Configure WooCommerce'              => esc_html__( 'Install and Configure WooCommerce', 'Divi' ),
	'Ecommerce'                                      => esc_html__( 'Ecommerce', 'Divi' ),
	'Images'                                         => esc_html__( 'Images', 'Divi' ),
	'Divi AI will fill your pages with ...'          => esc_html__( 'Divi AI will fill your pages with relevant photos and images automatically. Using stock photos is the fastest and most reliable option. You can also generate completely unique images using AI, which takes longer. ', 'Divi' ),
	'Use Stock Images (Faster)'                      => esc_html__( 'Use Stock Images (Faster)', 'Divi' ),
	'Generate Images With AI (Slower)'               => esc_html__( 'Generate Images With AI (Slower)', 'Divi' ),
	'Use Placeholder Images'                         => esc_html__( 'Use Placeholder Images', 'Divi' ),
	'Customize Fonts and Colors'                     => esc_html__( 'Customize Fonts and Colors', 'Divi' ),
	'Example: Our company, Sunset Solar'             => esc_html__( 'Example: Our company, Sunset Solar, is a solar panel installation company serving the San Francisco Bay Area', 'Divi' ),
	'Define custom fonts and colors'                 => esc_html__( 'Define custom fonts and colors below or leave empty to let AI choose for you', 'Divi' ),
	'Heading Font'                                   => esc_html__( 'Heading Font', 'Divi' ),
	'Body Font'                                      => esc_html__( 'Body Font', 'Divi' ),
	'Heading Font Color'                             => esc_html__( 'Heading Font Color', 'Divi' ),
	'Body Font Color'                                => esc_html__( 'Body Font Color', 'Divi' ),
	'Primary Color'                                  => esc_html__( 'Primary Color', 'Divi' ),
	'Secondary Color'                                => esc_html__( 'Secondary Color', 'Divi' ),
	'Search Fonts'                                   => esc_html__( 'Search Fonts', 'Divi' ),
	'Let AI Choose'                                  => esc_html__( 'Let AI Choose', 'Divi' ),
	'Default'                                        => esc_html__( 'Default', 'Divi' ),
	'Custom'                                         => esc_html__( 'Custom', 'Divi' ),
	'Divi AI Default'                                => esc_html__( 'Divi AI Default', 'Divi' ),
	'Website Default'                                => esc_html__( 'Website Default', 'Divi' ),
	'The heading font will ...'                      => esc_html__( 'The heading font will be used for all headings throughout the layout. Let AI pick a great font for you, or choose one yourself.', 'Divi' ),
	'The body font will ...'                         => esc_html__( 'The body font will be used for all text blocks throughout the layout. Let AI pick a great font for you, or choose one yourself.', 'Divi' ),
	'Pick a color for your headings ...'             => esc_html__( 'Pick a color for your headings, or let Divi AI choose one for you.', 'Divi' ),
	'Pick a color to use for things like ...'        => esc_html__( 'Pick a color to use for things like paragraphs, or let Divi AI choose one for you.', 'Divi' ),
	'Select a primary color ...'                     => esc_html__( 'Select a primary color, or let Divi AI choose a great color for you. The primary color is used through the layout, such as background colors, button colors, etc.', 'Divi' ),
	'Select a secondary color ...'                   => esc_html__( 'Select a secondary color, or let Divi AI choose a great color for you. The secondary color is used through the layout as a secondary accent color, such as for icon colors, text highlights, etc.', 'Divi' ),
	'Give Divi AI general information ...'           => esc_html__( 'Give Divi AI general information about your website or business. The more context it has about you and your goals, the better layouts it will create and the more relevant content it will write.', 'Divi' ),
	'Generate & Publish My Website'                  => __( 'Generate & Publish My Website', 'Divi' ),
	'Select this option if you plan ...'             => esc_html__( 'Select this option if you plan to sell products on your website. We will install WooCommerce and configure your cart and checkout pages.', 'Divi' ),
	'Enter your website name ...'                    => esc_html__( 'Enter your website name, which will be used for meta titles and throughout your website content.', 'Divi' ),
	'Enter your website slogan ...'                  => esc_html__( 'Enter your website slogan, which will be used in meta descriptions and as context for Divi AI.', 'Divi' ),
	'Generating Your Menu'                           => esc_html__( 'Generating Your Menu', 'Divi' ),
	'Generating %1$s Pages'                          => esc_html__( 'Generating %1$s Pages', 'Divi' ),
	'Creating %1$s Page'                             => esc_html__( 'Creating %1$s Page', 'Divi' ),
	'Importing Pages'                                => esc_html__( 'Importing Pages', 'Divi' ),
	'Designing Layout'                               => esc_html__( 'Designing Layout', 'Divi' ),
	'Writing Content'                                => esc_html__( 'Writing Content', 'Divi' ),
	'Generating Images'                              => esc_html__( 'Generating Images', 'Divi' ),
	'Add A Custom Page'                              => esc_html__( 'Add A Custom Page', 'Divi' ),
	'Page Name'                                      => esc_html__( 'Page Name', 'Divi' ),
	'Fetching Pages'                                 => esc_html__( 'Fetching Pages', 'Divi' ),
	'Uh Oh! Something Went Wrong'                    => esc_html__( 'Uh Oh! Something Went Wrong', 'Divi' ),
	'While creating your site'                       => esc_html__( 'While creating your site, something got hung up. Click Retry to resume where things left off, or Cancel to restore your site to its previous state.' ),
	'Retry'                                          => esc_html__( 'Retry', 'Divi' ),
	'Purchase A Divi AI Membership'                  => esc_html__( 'Purchase A Divi AI Membership', 'Divi' ),
	'Something went wrong'                           => esc_html__( 'Something went wrong', 'Divi' ),
	'Close'                                          => esc_html__( 'Close', 'Divi' ),
	'uploaded logo'                                  => esc_html__( 'uploaded logo', 'Divi' ),
	'Importing Presets'                              => esc_html__( 'Importing Presets', 'Divi' ),
	'Importing Customizer Settings'                  => esc_html__( 'Importing Customizer Settings', 'Divi' ),
	'Default Website Template'                       => esc_html__( 'Default Website Template', 'Divi' ),
	'Update'                                         => esc_html__( 'Update', 'Divi' ),
	'request it here'                                => esc_html__( 'request it here', 'Divi' ),
);
